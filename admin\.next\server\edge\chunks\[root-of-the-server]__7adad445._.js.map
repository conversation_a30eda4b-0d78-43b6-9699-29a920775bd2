{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/session.ts"], "sourcesContent": ["import { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Session configuration\nconst SESSION_COOKIE_NAME = 'benzochem-admin-session';\nconst CSRF_COOKIE_NAME = 'benzochem-csrf-token';\nconst SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\nconst CSRF_TOKEN_LENGTH = 32;\n\n// Get JWT secret from environment variable\nfunction getJWTSecret(): Uint8Array {\n  const secret = process.env.JWT_SECRET || 'benzochem-admin-jwt-secret-change-in-production';\n  return new TextEncoder().encode(secret);\n}\n\n// Get session encryption key from environment variable\nfunction getSessionKey(): string {\n  return process.env.SESSION_SECRET || 'benzochem-session-secret-must-be-at-least-32-characters-long-change-in-production';\n}\n\n// Admin session data interface\nexport interface AdminSession {\n  adminId: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'admin' | 'super_admin';\n  permissions: string[];\n  isActive: boolean;\n  loginTime: number;\n  expiresAt: number;\n}\n\n// CSRF token generation\nexport function generateCSRFToken(): string {\n  const array = new Uint8Array(CSRF_TOKEN_LENGTH);\n  crypto.getRandomValues(array);\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n}\n\n// Create JWT token for session\nexport async function createSessionToken(adminData: Omit<AdminSession, 'loginTime' | 'expiresAt'>): Promise<string> {\n  const now = Date.now();\n  const expiresAt = now + SESSION_DURATION;\n  \n  const payload: AdminSession = {\n    ...adminData,\n    loginTime: now,\n    expiresAt,\n  };\n\n  const token = await new SignJWT(payload)\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt(now / 1000)\n    .setExpirationTime(expiresAt / 1000)\n    .setIssuer('benzochem-admin')\n    .setAudience('benzochem-admin-dashboard')\n    .sign(getJWTSecret());\n\n  return token;\n}\n\n// Verify and decode JWT token\nexport async function verifySessionToken(token: string): Promise<AdminSession | null> {\n  try {\n    console.log('verifySessionToken: Verifying token...');\n    const { payload } = await jwtVerify(token, getJWTSecret(), {\n      issuer: 'benzochem-admin',\n      audience: 'benzochem-admin-dashboard',\n    });\n\n    const session = payload as unknown as AdminSession;\n    console.log('verifySessionToken: Token verified, checking expiration...');\n    console.log('verifySessionToken: Session expires at:', new Date(session.expiresAt));\n    console.log('verifySessionToken: Current time:', new Date());\n\n    // Check if session has expired\n    if (session.expiresAt < Date.now()) {\n      console.log('verifySessionToken: Session has expired');\n      return null;\n    }\n\n    console.log('verifySessionToken: Session is valid');\n    return session;\n  } catch (error) {\n    console.error('Session token verification failed:', error);\n    return null;\n  }\n}\n\n// Set secure session cookie\nexport function setSessionCookie(response: NextResponse, token: string): void {\n  const isProduction = process.env.NODE_ENV === 'production';\n  \n  response.cookies.set(SESSION_COOKIE_NAME, token, {\n    httpOnly: true,\n    secure: isProduction, // Only use secure in production (HTTPS)\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000, // Convert to seconds\n    path: '/',\n  });\n}\n\n// Set CSRF token cookie\nexport function setCSRFCookie(response: NextResponse, csrfToken: string): void {\n  const isProduction = process.env.NODE_ENV === 'production';\n  \n  response.cookies.set(CSRF_COOKIE_NAME, csrfToken, {\n    httpOnly: false, // CSRF token needs to be accessible to client-side JavaScript\n    secure: isProduction,\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000,\n    path: '/',\n  });\n}\n\n// Get session from request cookies\nexport async function getSessionFromRequest(request: NextRequest): Promise<AdminSession | null> {\n  const token = request.cookies.get(SESSION_COOKIE_NAME)?.value;\n  console.log('getSessionFromRequest: Token found:', !!token);\n\n  if (!token) {\n    console.log('getSessionFromRequest: No token found');\n    return null;\n  }\n\n  const session = await verifySessionToken(token);\n  console.log('getSessionFromRequest: Session verified:', !!session);\n  return session;\n}\n\n// Get session from server-side cookies\nexport async function getServerSession(): Promise<AdminSession | null> {\n  const cookieStore = cookies();\n  const token = cookieStore.get(SESSION_COOKIE_NAME)?.value;\n  \n  if (!token) {\n    return null;\n  }\n\n  return await verifySessionToken(token);\n}\n\n// Clear session cookies\nexport function clearSessionCookies(response: NextResponse): void {\n  response.cookies.delete(SESSION_COOKIE_NAME);\n  response.cookies.delete(CSRF_COOKIE_NAME);\n}\n\n// Validate CSRF token\nexport function validateCSRFToken(request: NextRequest, providedToken: string): boolean {\n  const cookieToken = request.cookies.get(CSRF_COOKIE_NAME)?.value;\n  return cookieToken === providedToken && cookieToken !== undefined;\n}\n\n// Session cookie configuration for iron-session (alternative approach)\nexport const sessionOptions = {\n  cookieName: SESSION_COOKIE_NAME,\n  password: getSessionKey(),\n  cookieOptions: {\n    secure: process.env.NODE_ENV === 'production',\n    httpOnly: true,\n    sameSite: 'lax' as const,\n    maxAge: SESSION_DURATION / 1000,\n  },\n};\n\n// Type for iron-session\ndeclare module 'iron-session' {\n  interface IronSessionData {\n    admin?: AdminSession;\n    csrfToken?: string;\n  }\n}\n\n// Refresh session token (extend expiration)\nexport async function refreshSessionToken(currentToken: string): Promise<string | null> {\n  const session = await verifySessionToken(currentToken);\n  \n  if (!session) {\n    return null;\n  }\n\n  // Create new token with extended expiration\n  const refreshedSession: Omit<AdminSession, 'loginTime' | 'expiresAt'> = {\n    adminId: session.adminId,\n    email: session.email,\n    firstName: session.firstName,\n    lastName: session.lastName,\n    role: session.role,\n    permissions: session.permissions,\n    isActive: session.isActive,\n  };\n\n  return await createSessionToken(refreshedSession);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AACA;AAAA;;;AAGA,wBAAwB;AACxB,MAAM,sBAAsB;AAC5B,MAAM,mBAAmB;AACzB,MAAM,mBAAmB,KAAK,KAAK,KAAK,MAAM,2BAA2B;AACzE,MAAM,oBAAoB;AAE1B,2CAA2C;AAC3C,SAAS;IACP,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;IACzC,OAAO,IAAI,cAAc,MAAM,CAAC;AAClC;AAEA,uDAAuD;AACvD,SAAS;IACP,OAAO,QAAQ,GAAG,CAAC,cAAc,IAAI;AACvC;AAgBO,SAAS;IACd,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAGO,eAAe,mBAAmB,SAAwD;IAC/F,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,MAAM;IAExB,MAAM,UAAwB;QAC5B,GAAG,SAAS;QACZ,WAAW;QACX;IACF;IAEA,MAAM,QAAQ,MAAM,IAAI,6JAAA,CAAA,UAAO,CAAC,SAC7B,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,CAAC,MAAM,MAClB,iBAAiB,CAAC,YAAY,MAC9B,SAAS,CAAC,mBACV,WAAW,CAAC,6BACZ,IAAI,CAAC;IAER,OAAO;AACT;AAGO,eAAe,mBAAmB,KAAa;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE,OAAO,gBAAgB;YACzD,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,UAAU;QAChB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,2CAA2C,IAAI,KAAK,QAAQ,SAAS;QACjF,QAAQ,GAAG,CAAC,qCAAqC,IAAI;QAErD,+BAA+B;QAC/B,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,IAAI;YAClC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,QAAsB,EAAE,KAAa;IACpE,MAAM,eAAe,oDAAyB;IAE9C,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO;QAC/C,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,mBAAmB;QAC3B,MAAM;IACR;AACF;AAGO,SAAS,cAAc,QAAsB,EAAE,SAAiB;IACrE,MAAM,eAAe,oDAAyB;IAE9C,SAAS,OAAO,CAAC,GAAG,CAAC,kBAAkB,WAAW;QAChD,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,mBAAmB;QAC3B,MAAM;IACR;AACF;AAGO,eAAe,sBAAsB,OAAoB;IAC9D,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACxD,QAAQ,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,IAAI,CAAC,OAAO;QACV,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mBAAmB;IACzC,QAAQ,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,QAAQ,YAAY,GAAG,CAAC,sBAAsB;IAEpD,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO,MAAM,mBAAmB;AAClC;AAGO,SAAS,oBAAoB,QAAsB;IACxD,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;AAC1B;AAGO,SAAS,kBAAkB,OAAoB,EAAE,aAAqB;IAC3E,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC3D,OAAO,gBAAgB,iBAAiB,gBAAgB;AAC1D;AAGO,MAAM,iBAAiB;IAC5B,YAAY;IACZ,UAAU;IACV,eAAe;QACb,QAAQ,oDAAyB;QACjC,UAAU;QACV,UAAU;QACV,QAAQ,mBAAmB;IAC7B;AACF;AAWO,eAAe,oBAAoB,YAAoB;IAC5D,MAAM,UAAU,MAAM,mBAAmB;IAEzC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,mBAAkE;QACtE,SAAS,QAAQ,OAAO;QACxB,OAAO,QAAQ,KAAK;QACpB,WAAW,QAAQ,SAAS;QAC5B,UAAU,QAAQ,QAAQ;QAC1B,MAAM,QAAQ,IAAI;QAClB,aAAa,QAAQ,WAAW;QAChC,UAAU,QAAQ,QAAQ;IAC5B;IAEA,OAAO,MAAM,mBAAmB;AAClC"}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getSessionFromRequest } from '@/lib/session';\n\n// Define protected routes that require authentication\nconst PROTECTED_ROUTES = [\n  '/dashboard',\n  '/users',\n  '/products',\n  '/orders',\n  '/inventory',\n  '/reports',\n  '/collections',\n  '/settings',\n  '/api/admin', // Protect admin API routes\n];\n\n// Define public routes that don't require authentication\nconst PUBLIC_ROUTES = [\n  '/login',\n  '/api/auth/login',\n  '/api/auth/logout',\n];\n\n// Define routes that should redirect to dashboard if already authenticated\nconst AUTH_ROUTES = ['/login'];\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Skip middleware for static files and Next.js internals\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/favicon.ico') ||\n    pathname.startsWith('/static/') ||\n    pathname.includes('.')\n  ) {\n    return NextResponse.next();\n  }\n\n  // Check if the route is protected\n  const isProtectedRoute = PROTECTED_ROUTES.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Check if the route is public\n  const isPublicRoute = PUBLIC_ROUTES.some(route => \n    pathname === route || pathname.startsWith(route)\n  );\n\n  // Check if the route is an auth route (login/setup)\n  const isAuthRoute = AUTH_ROUTES.some(route => \n    pathname === route || pathname.startsWith(route)\n  );\n\n  try {\n    // Get session from request\n    const session = await getSessionFromRequest(request);\n    const isAuthenticated = !!session && session.isActive;\n\n    // Handle protected routes\n    if (isProtectedRoute) {\n      if (!isAuthenticated) {\n        // Redirect to login if not authenticated\n        const loginUrl = new URL('/login', request.url);\n        loginUrl.searchParams.set('redirect', pathname);\n        return NextResponse.redirect(loginUrl);\n      }\n\n      // Add session info to request headers for API routes\n      if (pathname.startsWith('/api/admin')) {\n        const requestHeaders = new Headers(request.headers);\n        requestHeaders.set('x-admin-id', session.adminId);\n        requestHeaders.set('x-admin-role', session.role);\n        requestHeaders.set('x-admin-permissions', JSON.stringify(session.permissions));\n\n        return NextResponse.next({\n          request: {\n            headers: requestHeaders,\n          },\n        });\n      }\n\n      return NextResponse.next();\n    }\n\n    // Handle auth routes (login only)\n    if (isAuthRoute) {\n      if (isAuthenticated) {\n        // Redirect to dashboard if already authenticated\n        return NextResponse.redirect(new URL('/dashboard', request.url));\n      }\n\n      // For login route, always allow access\n      return NextResponse.next();\n    }\n\n    // Handle public routes\n    if (isPublicRoute) {\n      return NextResponse.next();\n    }\n\n    // Handle root route\n    if (pathname === '/') {\n      if (isAuthenticated) {\n        return NextResponse.redirect(new URL('/dashboard', request.url));\n      } else {\n        // Always redirect unauthenticated users to login\n        return NextResponse.redirect(new URL('/login', request.url));\n      }\n    }\n\n    // Default: allow the request to continue\n    return NextResponse.next();\n\n  } catch (error) {\n    console.error('Middleware error:', error);\n    \n    // On error, redirect to login for protected routes\n    if (isProtectedRoute) {\n      const loginUrl = new URL('/login', request.url);\n      loginUrl.searchParams.set('redirect', pathname);\n      return NextResponse.redirect(loginUrl);\n    }\n\n    // For other routes, continue normally\n    return NextResponse.next();\n  }\n}\n\n// Configure which routes the middleware should run on\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,sDAAsD;AACtD,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,yDAAyD;AACzD,MAAM,gBAAgB;IACpB;IACA;IACA;CACD;AAED,2EAA2E;AAC3E,MAAM,cAAc;IAAC;CAAS;AAEvB,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,yDAAyD;IACzD,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC,eACpB,SAAS,QAAQ,CAAC,MAClB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,kCAAkC;IAClC,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,CAAA,QAC7C,SAAS,UAAU,CAAC;IAGtB,+BAA+B;IAC/B,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAA,QACvC,aAAa,SAAS,SAAS,UAAU,CAAC;IAG5C,oDAAoD;IACpD,MAAM,cAAc,YAAY,IAAI,CAAC,CAAA,QACnC,aAAa,SAAS,SAAS,UAAU,CAAC;IAG5C,IAAI;QACF,2BAA2B;QAC3B,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE;QAC5C,MAAM,kBAAkB,CAAC,CAAC,WAAW,QAAQ,QAAQ;QAErD,0BAA0B;QAC1B,IAAI,kBAAkB;YACpB,IAAI,CAAC,iBAAiB;gBACpB,yCAAyC;gBACzC,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;gBAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;gBACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,qDAAqD;YACrD,IAAI,SAAS,UAAU,CAAC,eAAe;gBACrC,MAAM,iBAAiB,IAAI,QAAQ,QAAQ,OAAO;gBAClD,eAAe,GAAG,CAAC,cAAc,QAAQ,OAAO;gBAChD,eAAe,GAAG,CAAC,gBAAgB,QAAQ,IAAI;gBAC/C,eAAe,GAAG,CAAC,uBAAuB,KAAK,SAAS,CAAC,QAAQ,WAAW;gBAE5E,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;YAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,kCAAkC;QAClC,IAAI,aAAa;YACf,IAAI,iBAAiB;gBACnB,iDAAiD;gBACjD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;YAChE;YAEA,uCAAuC;YACvC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,uBAAuB;QACvB,IAAI,eAAe;YACjB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,oBAAoB;QACpB,IAAI,aAAa,KAAK;YACpB,IAAI,iBAAiB;gBACnB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;YAChE,OAAO;gBACL,iDAAiD;gBACjD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;YAC5D;QACF;QAEA,yCAAyC;QACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAE1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,mDAAmD;QACnD,IAAI,kBAAkB;YACpB,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;YAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,sCAAsC;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;AACF;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}