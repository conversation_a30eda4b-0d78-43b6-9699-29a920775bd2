"use client";

import { useState } from "react";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  Di<PERSON><PERSON>itle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Search, 
  MoreHorizontal, 
  Eye,
  CheckCircle,
  XCircle,
  FileText,
  Download,
  RefreshCw,
  Building,
  AlertTriangle
} from "lucide-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../../convex/_generated/api";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";

export default function GSTVerificationPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [verificationFilter, setVerificationFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const pageSize = 20;

  const { admin } = useAuth();

  // Queries
  const users = useQuery(api.users.getUsersWithGST, {
    search: searchTerm || undefined,
    verificationStatus: verificationFilter === "all" ? undefined : verificationFilter === "verified",
    limit: pageSize,
    offset: currentPage * pageSize,
  });

  const gstStats = useQuery(api.users.getGSTStats);

  // Mutations
  const updateGSTVerification = useMutation(api.users.updateGSTVerification);
  const getOrCreateDemoAdmin = useMutation(api.admins.getOrCreateDemoAdmin);

  const handleUpdateGSTVerification = async (userId: string, isVerified: boolean) => {
    if (!admin) return;

    try {
      // Get or create the demo admin in Convex
      const adminId = await getOrCreateDemoAdmin({ email: admin.email });

      await updateGSTVerification({
        userId: userId as any,
        isVerified,
        adminId: adminId,
      });
      toast.success(`GST verification ${isVerified ? 'approved' : 'revoked'} successfully`);
    } catch (error) {
      toast.error("Failed to update GST verification");
      console.error(error);
    }
  };

  const getVerificationBadge = (isVerified: boolean | undefined) => {
    if (isVerified === true) {
      return <Badge variant="default" className="bg-green-500">Verified</Badge>;
    } else if (isVerified === false) {
      return <Badge variant="destructive">Not Verified</Badge>;
    } else {
      return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const openDetailsDialog = (user: any) => {
    setSelectedUser(user);
    setShowDetailsDialog(true);
  };

  return (
    <ProtectedRoute requiredPermission="users.read">
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">GST Verification</h1>
              <p className="text-muted-foreground">
                Manage GST verification status for user accounts
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export GST Data
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total GST Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{gstStats?.total || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Verified</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{gstStats?.verified || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending Verification</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{gstStats?.pending || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Not Verified</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{gstStats?.notVerified || 0}</div>
              </CardContent>
            </Card>
          </div>

          {/* GST Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>GST Users</CardTitle>
              <CardDescription>
                Manage GST verification for business users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-6">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={verificationFilter} onValueChange={setVerificationFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by verification" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="not_verified">Not Verified</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>GST Number</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Registered</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users?.map((user) => (
                      <TableRow key={user._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {user.firstName} {user.lastName}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {user.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            {user.businessName && (
                              <div className="font-medium">{user.businessName}</div>
                            )}
                            {user.legalNameOfBusiness && (
                              <div className="text-sm text-muted-foreground">
                                {user.legalNameOfBusiness}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            {user.gstNumber ? (
                              <div className="font-mono text-sm">{user.gstNumber}</div>
                            ) : (
                              <span className="text-muted-foreground">No GST Number</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getVerificationBadge(user.isGstVerified)}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(user.createdAt)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openDetailsDialog(user)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {user.gstNumber && (
                                <>
                                  {!user.isGstVerified && (
                                    <DropdownMenuItem
                                      onClick={() => handleUpdateGSTVerification(user._id, true)}
                                      className="text-green-600"
                                    >
                                      <CheckCircle className="mr-2 h-4 w-4" />
                                      Verify GST
                                    </DropdownMenuItem>
                                  )}
                                  {user.isGstVerified && (
                                    <DropdownMenuItem
                                      onClick={() => handleUpdateGSTVerification(user._id, false)}
                                      className="text-red-600"
                                    >
                                      <XCircle className="mr-2 h-4 w-4" />
                                      Revoke Verification
                                    </DropdownMenuItem>
                                  )}
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, users?.length || 0)} of {users?.length || 0} users
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={!users || users.length < pageSize}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* GST Details Dialog */}
          <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>GST Details</DialogTitle>
                <DialogDescription>
                  Complete GST information for {selectedUser?.firstName} {selectedUser?.lastName}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Personal Information</h4>
                    <div className="space-y-1 text-sm">
                      <div>Name: {selectedUser?.firstName} {selectedUser?.lastName}</div>
                      <div>Email: {selectedUser?.email}</div>
                      {selectedUser?.phone && <div>Phone: {selectedUser?.phone}</div>}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Business Information</h4>
                    <div className="space-y-1 text-sm">
                      {selectedUser?.businessName && <div>Business: {selectedUser?.businessName}</div>}
                      {selectedUser?.legalNameOfBusiness && <div>Legal Name: {selectedUser?.legalNameOfBusiness}</div>}
                      {selectedUser?.tradeName && <div>Trade Name: {selectedUser?.tradeName}</div>}
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">GST Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      {selectedUser?.gstNumber && (
                        <div>GST Number: <span className="font-mono">{selectedUser?.gstNumber}</span></div>
                      )}
                      {selectedUser?.taxpayerType && <div>Taxpayer Type: {selectedUser?.taxpayerType}</div>}
                      {selectedUser?.gstStatus && <div>GST Status: {selectedUser?.gstStatus}</div>}
                    </div>
                    <div>
                      {selectedUser?.constitutionOfBusiness && <div>Constitution: {selectedUser?.constitutionOfBusiness}</div>}
                      {selectedUser?.principalPlaceOfBusiness && <div>Principal Place: {selectedUser?.principalPlaceOfBusiness}</div>}
                      <div>Verification Status: {getVerificationBadge(selectedUser?.isGstVerified)}</div>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowDetailsDialog(false)}>
                  Close
                </Button>
                {selectedUser?.gstNumber && (
                  <div className="flex gap-2">
                    {!selectedUser?.isGstVerified && (
                      <Button
                        onClick={() => {
                          handleUpdateGSTVerification(selectedUser._id, true);
                          setShowDetailsDialog(false);
                        }}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Verify GST
                      </Button>
                    )}
                    {selectedUser?.isGstVerified && (
                      <Button
                        variant="destructive"
                        onClick={() => {
                          handleUpdateGSTVerification(selectedUser._id, false);
                          setShowDetailsDialog(false);
                        }}
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Revoke Verification
                      </Button>
                    )}
                  </div>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
