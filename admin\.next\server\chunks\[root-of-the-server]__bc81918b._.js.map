{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/app/api/setup-admin/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { api } from '../../../../convex/_generated/api';\nimport { ConvexHttpClient } from 'convex/browser';\n\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email } = await request.json();\n    \n    if (!email) {\n      return NextResponse.json({ error: 'Email is required' }, { status: 400 });\n    }\n\n    // Update the admin user to be a super_admin with all permissions\n    await convex.mutation(api.admins.updateAdminRoleAndPermissions, {\n      email: email,\n      role: \"super_admin\",\n      permissions: [\n        \"users.read\", \"users.write\", \"users.approve\", \"users.delete\",\n        \"products.read\", \"products.write\", \"products.delete\",\n        \"admins.read\", \"admins.write\", \"admins.delete\",\n        \"settings.read\", \"settings.write\",\n        \"api_keys.read\", \"api_keys.write\", \"api_keys.delete\",\n        \"notifications.read\", \"notifications.write\",\n        \"logs.read\", \"reports.read\"\n      ]\n    });\n\n    return NextResponse.json({ \n      success: true, \n      message: 'Admin role and permissions updated successfully' \n    });\n  } catch (error) {\n    console.error('Setup admin error:', error);\n    return NextResponse.json({ \n      error: 'Failed to setup admin', \n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEA,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAE5B,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,iEAAiE;QACjE,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,MAAM,CAAC,6BAA6B,EAAE;YAC9D,OAAO;YACP,MAAM;YACN,aAAa;gBACX;gBAAc;gBAAe;gBAAiB;gBAC9C;gBAAiB;gBAAkB;gBACnC;gBAAe;gBAAgB;gBAC/B;gBAAiB;gBACjB;gBAAiB;gBAAkB;gBACnC;gBAAsB;gBACtB;gBAAa;aACd;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}