import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Query to get all admins (simplified)
export const getAdmins = query({
  args: {
    search: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Get all admins
    const allAdmins = await ctx.db.query("admins").collect();

    // Apply search filter
    let filteredAdmins = allAdmins;
    if (args.search) {
      filteredAdmins = allAdmins.filter(admin =>
        admin.firstName.toLowerCase().includes(args.search!.toLowerCase()) ||
        admin.lastName.toLowerCase().includes(args.search!.toLowerCase()) ||
        admin.email.toLowerCase().includes(args.search!.toLowerCase())
      );
    }

    // Sort by creation time (newest first)
    filteredAdmins.sort((a, b) => b._creationTime - a._creationTime);

    // Apply pagination
    const offset = args.offset || 0;
    const limit = args.limit || 50;
    const paginatedAdmins = filteredAdmins.slice(offset, offset + limit);

    // Remove passwords from response
    const adminsWithoutPasswords = paginatedAdmins.map(admin => {
      const { password, ...adminData } = admin;
      return adminData;
    });

    return {
      admins: adminsWithoutPasswords,
      total: filteredAdmins.length,
      hasMore: offset + limit < filteredAdmins.length,
    };
  },
});

// Query to get admin by ID (without password)
export const getAdminById = query({
  args: { id: v.id("admins") },
  handler: async (ctx, args) => {
    const admin = await ctx.db.get(args.id);
    if (!admin) return null;

    const { password, ...adminData } = admin;
    return adminData;
  },
});

// Query to get admin by email (without password)
export const getAdminByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    const admin = await ctx.db
      .query("admins")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (!admin) return null;

    const { password, ...adminData } = admin;
    return adminData;
  },
});

// Simple admin statistics query
export const getAdminStats = query({
  args: {},
  handler: async (ctx) => {
    const allAdmins = await ctx.db.query("admins").collect();
    return {
      total: allAdmins.length,
    };
  },
});




