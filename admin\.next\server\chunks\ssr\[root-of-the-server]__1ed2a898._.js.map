{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\nimport { Loader2 } from \"lucide-react\";\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredPermission?: string;\n  fallback?: React.ReactNode;\n}\n\nexport function ProtectedRoute({ \n  children, \n  requiredPermission, \n  fallback \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, hasPermission } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push(\"/login\");\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin\" />\n          <p className=\"text-sm text-muted-foreground\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null; // Will redirect to login\n  }\n\n  if (requiredPermission && !hasPermission(requiredPermission)) {\n    return (\n      fallback || (\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground\">\n              You don't have permission to access this page.\n            </p>\n          </div>\n        </div>\n      )\n    );\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,kBAAkB,EAClB,QAAQ,EACY;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC5D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIrD;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,yBAAyB;IACxC;IAEA,IAAI,sBAAsB,CAAC,cAAc,qBAAqB;QAC5D,OACE,0BACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAO/C;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { cn } from \"@/lib/utils\";\nimport {\n  LayoutDashboard,\n  Users,\n  Package,\n  Bell,\n  Settings,\n  Key,\n  Activity,\n  UserCog,\n  LogOut,\n  ChevronDown,\n  ChevronRight,\n} from \"lucide-react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { useState } from \"react\";\n\ninterface SidebarProps {\n  onNavigate?: () => void;\n}\n\ninterface NavItem {\n  title: string;\n  href?: string;\n  icon: React.ComponentType<{ className?: string }>;\n  badge?: string | number;\n  children?: NavItem[];\n  permission?: string;\n}\n\nexport function Sidebar({ onNavigate }: SidebarProps) {\n  const { admin, logout, hasPermission } = useAuth();\n  const pathname = usePathname();\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n\n  const toggleExpanded = (title: string) => {\n    setExpandedItems(prev =>\n      prev.includes(title)\n        ? prev.filter(item => item !== title)\n        : [...prev, title]\n    );\n  };\n\n  // Early return if auth context is not ready\n  if (!hasPermission) {\n    return (\n      <div className=\"flex h-full flex-col bg-card border-r\">\n        <div className=\"flex h-16 items-center border-b px-6\">\n          <div className=\"flex items-center gap-2\">\n            <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-lg\">B</span>\n            </div>\n            <div>\n              <h2 className=\"text-lg font-semibold\">Benzochem</h2>\n              <p className=\"text-xs text-muted-foreground\">Admin Dashboard</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex-1 flex items-center justify-center\">\n          <div className=\"text-sm text-muted-foreground\">Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  const navItems: NavItem[] = [\n    {\n      title: \"Dashboard\",\n      href: \"/dashboard\",\n      icon: LayoutDashboard,\n    },\n    {\n      title: \"User Management\",\n      icon: Users,\n      permission: \"users.read\",\n      children: [\n        {\n          title: \"All Users\",\n          href: \"/dashboard/users\",\n          icon: Users,\n          permission: \"users.read\",\n        },\n        {\n          title: \"Pending Approvals\",\n          href: \"/dashboard/users/pending\",\n          icon: Users,\n          badge: \"3\", // This would come from real data\n          permission: \"users.approve\",\n        },\n        {\n          title: \"GST Verification\",\n          href: \"/dashboard/users/gst\",\n          icon: Users,\n          permission: \"users.read\",\n        },\n      ],\n    },\n    {\n      title: \"Product Management\",\n      icon: Package,\n      permission: \"products.read\",\n      children: [\n        {\n          title: \"All Products\",\n          href: \"/dashboard/products\",\n          icon: Package,\n          permission: \"products.read\",\n        },\n        {\n          title: \"Featured Products\",\n          href: \"/dashboard/products/featured\",\n          icon: Package,\n          permission: \"products.read\",\n        },\n        {\n          title: \"Collections\",\n          href: \"/dashboard/products/collections\",\n          icon: Package,\n          permission: \"products.read\",\n        },\n      ],\n    },\n    {\n      title: \"Notifications\",\n      href: \"/dashboard/notifications\",\n      icon: Bell,\n      badge: \"5\", // This would come from real data\n      permission: \"notifications.read\",\n    },\n    {\n      title: \"Activity Logs\",\n      href: \"/dashboard/activity\",\n      icon: Activity,\n      permission: \"logs.read\",\n    },\n    {\n      title: \"System\",\n      icon: Settings,\n      permission: \"settings.read\",\n      children: [\n        {\n          title: \"Settings\",\n          href: \"/dashboard/settings\",\n          icon: Settings,\n          permission: \"settings.read\",\n        },\n        {\n          title: \"API Keys\",\n          href: \"/dashboard/api-keys\",\n          icon: Key,\n          permission: \"api_keys.read\",\n        },\n        {\n          title: \"Admin Users\",\n          href: \"/dashboard/admins\",\n          icon: UserCog,\n          permission: \"admins.read\",\n        },\n      ],\n    },\n  ];\n\n  const filteredNavItems = navItems.filter(item =>\n    !item.permission || (hasPermission && hasPermission(item.permission))\n  );\n\n  const renderNavItem = (item: NavItem, level = 0) => {\n    const hasChildren = item.children && item.children.length > 0;\n    const isExpanded = expandedItems.includes(item.title);\n    const isActive = item.href ? pathname === item.href : false;\n    const hasItemPermission = !item.permission || (hasPermission && hasPermission(item.permission));\n\n    if (!hasItemPermission) return null;\n\n    const filteredChildren = item.children?.filter(child =>\n      !child.permission || (hasPermission && hasPermission(child.permission))\n    );\n\n    if (hasChildren && (!filteredChildren || filteredChildren.length === 0)) {\n      return null;\n    }\n\n    const content = (\n      <div className={cn(\"space-y-1\", level > 0 && \"ml-4\")}>\n        <Button\n          variant={isActive ? \"secondary\" : \"ghost\"}\n          className={cn(\n            \"w-full justify-start gap-2 h-9\",\n            level > 0 && \"h-8 text-sm\"\n          )}\n          onClick={() => {\n            if (hasChildren) {\n              toggleExpanded(item.title);\n            } else if (item.href) {\n              onNavigate?.();\n            }\n          }}\n          asChild={!hasChildren}\n        >\n          {hasChildren ? (\n            <div className=\"flex items-center justify-between w-full\">\n              <div className=\"flex items-center gap-2\">\n                <item.icon className=\"h-4 w-4\" />\n                <span>{item.title}</span>\n                {item.badge && (\n                  <Badge variant=\"secondary\" className=\"ml-auto\">\n                    {item.badge}\n                  </Badge>\n                )}\n              </div>\n              {isExpanded ? (\n                <ChevronDown className=\"h-4 w-4\" />\n              ) : (\n                <ChevronRight className=\"h-4 w-4\" />\n              )}\n            </div>\n          ) : (\n            <Link href={item.href!} className=\"flex items-center gap-2 w-full\">\n              <item.icon className=\"h-4 w-4\" />\n              <span>{item.title}</span>\n              {item.badge && (\n                <Badge variant=\"secondary\" className=\"ml-auto\">\n                  {item.badge}\n                </Badge>\n              )}\n            </Link>\n          )}\n        </Button>\n\n        {hasChildren && isExpanded && filteredChildren && (\n          <div className=\"space-y-1\">\n            {filteredChildren.map((child) => (\n              <div key={child.title}>\n                {renderNavItem(child, level + 1)}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    );\n\n    return content;\n  };\n\n  return (\n    <div className=\"flex h-full flex-col bg-card border-r\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center border-b px-6\">\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n            <span className=\"text-primary-foreground font-bold text-lg\">B</span>\n          </div>\n          <div>\n            <h2 className=\"text-lg font-semibold\">Benzochem</h2>\n            <p className=\"text-xs text-muted-foreground\">Admin Dashboard</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 overflow-auto p-4\">\n        <nav className=\"space-y-2\">\n          {filteredNavItems.map((item) => (\n            <div key={item.title}>\n              {renderNavItem(item)}\n            </div>\n          ))}\n        </nav>\n      </div>\n\n      {/* User info and logout */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center gap-3 mb-3\">\n          <div className=\"w-8 h-8 bg-muted rounded-full flex items-center justify-center\">\n            <span className=\"text-sm font-medium\">\n              {admin?.firstName?.[0]}{admin?.lastName?.[0]}\n            </span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-medium truncate\">\n              {admin?.firstName} {admin?.lastName}\n            </p>\n            <p className=\"text-xs text-muted-foreground truncate\">\n              {admin?.email}\n            </p>\n          </div>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"w-full justify-start gap-2\"\n          onClick={logout}\n        >\n          <LogOut className=\"h-4 w-4\" />\n          Sign out\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AAtBA;;;;;;;;;;AAqCO,SAAS,QAAQ,EAAE,UAAU,EAAgB;IAClD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,SAC7B;mBAAI;gBAAM;aAAM;IAExB;IAEA,4CAA4C;IAC5C,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;0CAE9D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;8BAInD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIvD;IAEA,MAAM,WAAsB;QAC1B;YACE,OAAO;YACP,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;QACvB;QACA;YACE,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,YAAY;YACZ,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,oMAAA,CAAA,QAAK;oBACX,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,oMAAA,CAAA,QAAK;oBACX,OAAO;oBACP,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,oMAAA,CAAA,QAAK;oBACX,YAAY;gBACd;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,YAAY;YACZ,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,wMAAA,CAAA,UAAO;oBACb,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,wMAAA,CAAA,UAAO;oBACb,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,wMAAA,CAAA,UAAO;oBACb,YAAY;gBACd;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;YACZ,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,0MAAA,CAAA,WAAQ;oBACd,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,gMAAA,CAAA,MAAG;oBACT,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,4MAAA,CAAA,UAAO;oBACb,YAAY;gBACd;aACD;QACH;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,OACvC,CAAC,KAAK,UAAU,IAAK,iBAAiB,cAAc,KAAK,UAAU;IAGrE,MAAM,gBAAgB,CAAC,MAAe,QAAQ,CAAC;QAC7C,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,KAAK;QACpD,MAAM,WAAW,KAAK,IAAI,GAAG,aAAa,KAAK,IAAI,GAAG;QACtD,MAAM,oBAAoB,CAAC,KAAK,UAAU,IAAK,iBAAiB,cAAc,KAAK,UAAU;QAE7F,IAAI,CAAC,mBAAmB,OAAO;QAE/B,MAAM,mBAAmB,KAAK,QAAQ,EAAE,OAAO,CAAA,QAC7C,CAAC,MAAM,UAAU,IAAK,iBAAiB,cAAc,MAAM,UAAU;QAGvE,IAAI,eAAe,CAAC,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,CAAC,GAAG;YACvE,OAAO;QACT;QAEA,MAAM,wBACJ,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,KAAK;;8BAC3C,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,WAAW,cAAc;oBAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kCACA,QAAQ,KAAK;oBAEf,SAAS;wBACP,IAAI,aAAa;4BACf,eAAe,KAAK,KAAK;wBAC3B,OAAO,IAAI,KAAK,IAAI,EAAE;4BACpB;wBACF;oBACF;oBACA,SAAS,CAAC;8BAET,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,KAAK,KAAK;;;;;;oCAChB,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC,KAAK,KAAK;;;;;;;;;;;;4BAIhB,2BACC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;6CAI5B,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,KAAK,IAAI;wBAAG,WAAU;;0CAChC,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CAAM,KAAK,KAAK;;;;;;4BAChB,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;gBAOpB,eAAe,cAAc,kCAC5B,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,sBACrB,8OAAC;sCACE,cAAc,OAAO,QAAQ;2BADtB,MAAM,KAAK;;;;;;;;;;;;;;;;QAS/B,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;;;;;;sCAE9D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;sCACE,cAAc;2BADP,KAAK,KAAK;;;;;;;;;;;;;;;0BAQ1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCACb,OAAO,WAAW,CAAC,EAAE;wCAAE,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CACV,OAAO;4CAAU;4CAAE,OAAO;;;;;;;kDAE7B,8OAAC;wCAAE,WAAU;kDACV,OAAO;;;;;;;;;;;;;;;;;;kCAId,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { Moon, Sun } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\n\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme();\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;;sCAC3B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1407, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\nimport { ThemeToggle } from \"@/components/theme-toggle\";\nimport {\n  Bell,\n  Search,\n  Settings,\n  User,\n  LogOut,\n} from \"lucide-react\";\nimport { Input } from \"@/components/ui/input\";\n\nexport function Header() {\n  const { admin, logout } = useAuth();\n\n  return (\n    <header className=\"sticky top-0 z-30 flex h-16 items-center justify-between gap-4 border-b bg-background px-6\">\n      {/* Search */}\n      <div className=\"flex-1 max-w-md\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n          <Input\n            placeholder=\"Search users, products, or settings...\"\n            className=\"pl-10\"\n          />\n        </div>\n      </div>\n\n      {/* Right side actions */}\n      <div className=\"flex items-center gap-2\">\n        {/* Notifications */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <Badge \n                variant=\"destructive\" \n                className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\"\n              >\n                3\n              </Badge>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\" className=\"w-80\">\n            <DropdownMenuLabel>Notifications</DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <div className=\"space-y-2 p-2\">\n              <div className=\"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">New user registration</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    John Doe has registered and is pending approval\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">2 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">Product updated</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Benzene product information has been updated\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">5 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted\">\n                <div className=\"w-2 h-2 bg-yellow-500 rounded-full mt-2\"></div>\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">System alert</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    High number of pending user approvals\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">10 minutes ago</p>\n                </div>\n              </div>\n            </div>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem className=\"w-full justify-center\">\n              View all notifications\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n\n        {/* Theme toggle */}\n        <ThemeToggle />\n\n        {/* User menu */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-9 w-9 rounded-full\">\n              <Avatar className=\"h-9 w-9\">\n                <AvatarFallback className=\"text-sm\">\n                  {admin?.firstName?.[0]}{admin?.lastName?.[0]}\n                </AvatarFallback>\n              </Avatar>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal\">\n              <div className=\"flex flex-col space-y-1\">\n                <p className=\"text-sm font-medium leading-none\">\n                  {admin?.firstName} {admin?.lastName}\n                </p>\n                <p className=\"text-xs leading-none text-muted-foreground\">\n                  {admin?.email}\n                </p>\n                <Badge variant=\"outline\" className=\"w-fit mt-1\">\n                  Admin\n                </Badge>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Profile</span>\n            </DropdownMenuItem>\n            <DropdownMenuItem>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Settings</span>\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem onClick={logout}>\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              <span>Log out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAtBA;;;;;;;;;;AAwBO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEhC,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC,iIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAKL,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,8OAAC,4IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;kDAInD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAOxD,8OAAC,qIAAA,CAAA,cAAW;;;;;kCAGZ,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;;gDACvB,OAAO,WAAW,CAAC,EAAE;gDAAE,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAKpD,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,WAAU;gCAAO,OAAM;gCAAM,UAAU;;kDAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;wDACV,OAAO;wDAAU;wDAAE,OAAO;;;;;;;8DAE7B,8OAAC;oDAAE,WAAU;8DACV,OAAO;;;;;;8DAEV,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAKpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,mBAAgB;;0DACf,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCAAC,SAAS;;0DACzB,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport { Sidebar } from \"./sidebar\";\nimport { Header } from \"./header\";\nimport { Menu } from \"lucide-react\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { isAuthenticated } = useAuth();\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"fixed top-4 left-4 z-40 md:hidden\"\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n        </SheetTrigger>\n        <SheetContent side=\"left\" className=\"p-0 w-72\">\n          <Sidebar onNavigate={() => setSidebarOpen(false)} />\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:fixed md:inset-y-0 md:flex md:w-72 md:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-72\">\n        <Header />\n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAcO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAElC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;;kCACtC,8OAAC,iIAAA,CAAA,eAAY;wBAAC,OAAO;kCACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGpB,8OAAC,iIAAA,CAAA,eAAY;wBAAC,MAAK;wBAAO,WAAU;kCAClC,cAAA,8OAAC,uIAAA,CAAA,UAAO;4BAAC,YAAY,IAAM,eAAe;;;;;;;;;;;;;;;;;0BAK9C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;0BAIV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 2027, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2242, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { XIcon } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalContentProps {\n  children: React.ReactNode\n  className?: string\n  showCloseButton?: boolean\n  onClose?: () => void\n}\n\ninterface ModalHeaderProps {\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalFooterProps {\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalTitleProps {\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalDescriptionProps {\n  children: React.ReactNode\n  className?: string\n}\n\nfunction Modal({ open, onOpenChange, children, className }: ModalProps) {\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === \"Escape\") {\n        onOpenChange(false)\n      }\n    }\n\n    if (open) {\n      document.addEventListener(\"keydown\", handleEscape)\n      document.body.style.overflow = \"hidden\"\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleEscape)\n      document.body.style.overflow = \"unset\"\n    }\n  }, [open, onOpenChange])\n\n  if (!open) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black/50 backdrop-blur-sm\"\n        onClick={() => onOpenChange(false)}\n      />\n      {/* Modal Content */}\n      <div className={cn(\"relative z-10\", className)}>\n        {children}\n      </div>\n    </div>\n  )\n}\n\nfunction ModalContent({\n  children,\n  className,\n  showCloseButton = true,\n  onClose\n}: ModalContentProps) {\n  return (\n    <div\n      className={cn(\n        \"bg-background rounded-xl border border-border/50 shadow-2xl\",\n        \"backdrop-blur-sm bg-background/95 supports-[backdrop-filter]:bg-background/80\",\n        \"animate-in fade-in-0 zoom-in-95 slide-in-from-top-[2%] duration-300\",\n        \"max-h-[95vh] overflow-hidden flex flex-col\",\n        className\n      )}\n    >\n      {children}\n      {showCloseButton && onClose && (\n        <button\n          className=\"absolute top-4 right-4 rounded-xs opacity-70 transition-all duration-200 hover:opacity-100 hover:bg-accent hover:scale-110 focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-none disabled:pointer-events-none p-2\"\n          onClick={onClose}\n        >\n          <XIcon className=\"h-4 w-4\" />\n          <span className=\"sr-only\">Close</span>\n        </button>\n      )}\n    </div>\n  )\n}\n\nfunction ModalHeader({ children, className }: ModalHeaderProps) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col gap-3 text-center sm:text-left px-8 pt-6 pb-4\",\n        \"border-b border-border/50 bg-muted/20\",\n        className\n      )}\n    >\n      {children}\n    </div>\n  )\n}\n\nfunction ModalFooter({ children, className }: ModalFooterProps) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col-reverse gap-3 sm:flex-row sm:justify-end px-8 py-6\",\n        \"border-t border-border/50 bg-muted/10\",\n        className\n      )}\n    >\n      {children}\n    </div>\n  )\n}\n\nfunction ModalTitle({ children, className }: ModalTitleProps) {\n  return (\n    <h2\n      className={cn(\n        \"text-xl font-semibold leading-tight tracking-tight text-foreground\",\n        className\n      )}\n    >\n      {children}\n    </h2>\n  )\n}\n\nfunction ModalDescription({ children, className }: ModalDescriptionProps) {\n  return (\n    <p\n      className={cn(\n        \"text-muted-foreground text-sm leading-relaxed\",\n        className\n      )}\n    >\n      {children}\n    </p>\n  )\n}\n\nexport {\n  Modal,\n  ModalContent,\n  ModalHeader,\n  ModalFooter,\n  ModalTitle,\n  ModalDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAwCA,SAAS,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAc;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,aAAa;YACf;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,aAAa;;;;;;0BAG9B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;0BACjC;;;;;;;;;;;;AAIT;AAEA,SAAS,aAAa,EACpB,QAAQ,EACR,SAAS,EACT,kBAAkB,IAAI,EACtB,OAAO,EACW;IAClB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,iFACA,uEACA,8CACA;;YAGD;YACA,mBAAmB,yBAClB,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC,gMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IAC5D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,yCACA;kBAGD;;;;;;AAGP;AAEA,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IAC5D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA,yCACA;kBAGD;;;;;;AAGP;AAEA,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAmB;IAC1D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;kBAGD;;;;;;AAGP;AAEA,SAAS,iBAAiB,EAAE,QAAQ,EAAE,SAAS,EAAyB;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2614, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm duration-300 transition-all ease-out\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[2%] data-[state=open]:slide-in-from-top-[2%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-0 rounded-xl border border-border/50 shadow-2xl duration-300 sm:max-w-lg\",\n          \"backdrop-blur-sm bg-background/95 supports-[backdrop-filter]:bg-background/80\",\n          \"transition-all ease-out\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-all duration-200 hover:opacity-100 hover:bg-accent hover:scale-110 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\n        \"flex flex-col gap-3 text-center sm:text-left px-4 pt-4 pb-3 sm:px-6 sm:pt-6 sm:pb-4\",\n        \"border-b border-border/50 bg-muted/20\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-3 sm:flex-row sm:justify-end px-4 py-3 sm:px-6 sm:py-4\",\n        \"border-t border-border/50 bg-muted/10\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\n        \"text-xl font-semibold leading-tight tracking-tight text-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\n        \"text-muted-foreground text-sm leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gdACA,iFACA,2BACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,yCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA,yCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2788, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/enhanced-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"./dialog\"\nimport { <PERSON><PERSON> } from \"./button\"\nimport { Loader2 } from \"lucide-react\"\n\ninterface EnhancedDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description?: string\n  children: React.ReactNode\n  footer?: React.ReactNode\n  className?: string\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"3xl\" | \"4xl\" | \"5xl\"\n  loading?: boolean\n}\n\nconst sizeClasses = {\n  sm: \"w-full max-w-sm mx-4 sm:mx-auto\",\n  md: \"w-full max-w-md mx-4 sm:mx-auto\",\n  lg: \"w-full max-w-lg mx-4 sm:mx-auto\",\n  xl: \"w-full max-w-xl mx-4 sm:mx-auto\",\n  \"2xl\": \"w-full max-w-2xl mx-4 sm:mx-auto\",\n  \"3xl\": \"w-full max-w-3xl mx-4 sm:mx-auto\",\n  \"4xl\": \"w-full max-w-4xl mx-4 sm:mx-auto\",\n  \"5xl\": \"w-full max-w-5xl mx-4 sm:mx-auto\",\n}\n\nexport function EnhancedDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  children,\n  footer,\n  className,\n  size = \"lg\",\n  loading = false,\n}: EnhancedDialogProps) {\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent\n        className={cn(\n          sizeClasses[size],\n          \"max-h-[90vh] overflow-hidden flex flex-col\",\n          className\n        )}\n        aria-labelledby=\"dialog-title\"\n        aria-describedby={description ? \"dialog-description\" : undefined}\n        role=\"dialog\"\n        aria-modal=\"true\"\n      >\n        <DialogHeader>\n          <DialogTitle\n            id=\"dialog-title\"\n            className=\"flex items-center gap-2\"\n          >\n            {loading && (\n              <Loader2\n                className=\"h-4 w-4 animate-spin\"\n                aria-hidden=\"true\"\n              />\n            )}\n            {title}\n          </DialogTitle>\n          {description && (\n            <DialogDescription id=\"dialog-description\">\n              {description}\n            </DialogDescription>\n          )}\n        </DialogHeader>\n\n        <div\n          className=\"flex-1 overflow-y-auto px-4 py-4 sm:px-6\"\n          role=\"main\"\n          tabIndex={-1}\n        >\n          {children}\n        </div>\n\n        {footer && (\n          <DialogFooter role=\"group\" aria-label=\"Dialog actions\">\n            {footer}\n          </DialogFooter>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface FormSectionProps {\n  title: string\n  description?: string\n  children: React.ReactNode\n  className?: string\n}\n\nexport function FormSection({ title, description, children, className }: FormSectionProps) {\n  return (\n    <div className={cn(\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\", className)}>\n      <div className=\"space-y-1\">\n        <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n          {title}\n        </h3>\n        {description && (\n          <p className=\"text-sm text-muted-foreground leading-relaxed transition-colors duration-200\">\n            {description}\n          </p>\n        )}\n      </div>\n      <div className=\"space-y-4 pt-2\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\ninterface FormFieldProps {\n  label: string\n  description?: string\n  required?: boolean\n  error?: string\n  children: React.ReactNode\n  className?: string\n}\n\nexport function FormField({\n  label,\n  description,\n  required = false,\n  error,\n  children,\n  className\n}: FormFieldProps) {\n  const fieldId = React.useId()\n  const errorId = error ? `${fieldId}-error` : undefined\n  const descriptionId = description ? `${fieldId}-description` : undefined\n\n  return (\n    <div className={cn(\"space-y-2 animate-in fade-in-0 slide-in-from-left-1 duration-300\", className)}>\n      <div className=\"space-y-1\">\n        <label\n          htmlFor={fieldId}\n          className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 transition-colors duration-200\"\n        >\n          {label}\n          {required && (\n            <>\n              <span className=\"text-destructive ml-1\" aria-hidden=\"true\">*</span>\n              <span className=\"sr-only\">(required)</span>\n            </>\n          )}\n        </label>\n        {description && (\n          <p\n            id={descriptionId}\n            className=\"text-xs text-muted-foreground leading-relaxed\"\n          >\n            {description}\n          </p>\n        )}\n      </div>\n      {React.cloneElement(children as React.ReactElement<any>, {\n        id: fieldId,\n        'aria-describedby': [descriptionId, errorId].filter(Boolean).join(' ') || undefined,\n        'aria-invalid': !!error,\n        'aria-required': required,\n      })}\n      {error && (\n        <p\n          id={errorId}\n          className=\"text-xs text-destructive leading-relaxed\"\n          role=\"alert\"\n          aria-live=\"polite\"\n        >\n          {error}\n        </p>\n      )}\n    </div>\n  )\n}\n\ninterface ConfirmDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description: string\n  confirmText?: string\n  cancelText?: string\n  onConfirm: () => void\n  onCancel?: () => void\n  variant?: \"default\" | \"destructive\"\n  loading?: boolean\n}\n\nexport function ConfirmDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = \"Confirm\",\n  cancelText = \"Cancel\",\n  onConfirm,\n  onCancel,\n  variant = \"default\",\n  loading = false,\n}: ConfirmDialogProps) {\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel()\n    } else {\n      onOpenChange(false)\n    }\n  }\n\n  const handleConfirm = () => {\n    onConfirm()\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent\n        className=\"w-full max-w-md mx-4 sm:mx-auto\"\n        aria-labelledby=\"confirm-dialog-title\"\n        aria-describedby=\"confirm-dialog-description\"\n        role=\"alertdialog\"\n        aria-modal=\"true\"\n      >\n        <DialogHeader>\n          <DialogTitle id=\"confirm-dialog-title\">\n            {title}\n          </DialogTitle>\n          <DialogDescription\n            id=\"confirm-dialog-description\"\n            className=\"leading-relaxed\"\n          >\n            {description}\n          </DialogDescription>\n        </DialogHeader>\n\n        <DialogFooter role=\"group\" aria-label=\"Confirmation actions\">\n          <Button\n            variant=\"outline\"\n            onClick={handleCancel}\n            disabled={loading}\n            aria-label={`${cancelText} this action`}\n          >\n            {cancelText}\n          </Button>\n          <Button\n            variant={variant}\n            onClick={handleConfirm}\n            disabled={loading}\n            aria-label={`${confirmText} this action`}\n            autoFocus\n          >\n            {loading && (\n              <Loader2\n                className=\"h-4 w-4 animate-spin mr-2\"\n                aria-hidden=\"true\"\n              />\n            )}\n            {confirmText}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface DetailDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  subtitle?: string\n  children: React.ReactNode\n  actions?: React.ReactNode\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"3xl\" | \"4xl\" | \"5xl\"\n}\n\nexport function DetailDialog({\n  open,\n  onOpenChange,\n  title,\n  subtitle,\n  children,\n  actions,\n  size = \"2xl\",\n}: DetailDialogProps) {\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent\n        className={cn(\n          sizeClasses[size],\n          \"max-h-[90vh] overflow-hidden flex flex-col\"\n        )}\n        aria-labelledby=\"detail-dialog-title\"\n        aria-describedby={subtitle ? \"detail-dialog-subtitle\" : undefined}\n        role=\"dialog\"\n        aria-modal=\"true\"\n      >\n        <DialogHeader>\n          <DialogTitle id=\"detail-dialog-title\">\n            {title}\n          </DialogTitle>\n          {subtitle && (\n            <DialogDescription id=\"detail-dialog-subtitle\">\n              {subtitle}\n            </DialogDescription>\n          )}\n        </DialogHeader>\n\n        <div\n          className=\"flex-1 overflow-y-auto px-4 py-4 sm:px-6\"\n          role=\"main\"\n          tabIndex={-1}\n          aria-label=\"Detail content\"\n        >\n          {children}\n        </div>\n\n        {actions && (\n          <DialogFooter role=\"group\" aria-label=\"Detail actions\">\n            {actions}\n          </DialogFooter>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface DetailSectionProps {\n  title: string\n  children: React.ReactNode\n  className?: string\n  columns?: 1 | 2 | 3\n}\n\nexport function DetailSection({ title, children, className, columns = 2 }: DetailSectionProps) {\n  const sectionId = React.useId()\n  const gridClasses = {\n    1: \"grid-cols-1\",\n    2: \"grid-cols-1 sm:grid-cols-2\",\n    3: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\",\n  }\n\n  return (\n    <section\n      className={cn(\"space-y-4\", className)}\n      aria-labelledby={`${sectionId}-title`}\n    >\n      <h4\n        id={`${sectionId}-title`}\n        className=\"font-semibold text-lg border-b border-border/30 pb-2\"\n      >\n        {title}\n      </h4>\n      <div\n        className={cn(\"grid gap-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-300\", gridClasses[columns])}\n        role=\"group\"\n        aria-labelledby={`${sectionId}-title`}\n      >\n        {children}\n      </div>\n    </section>\n  )\n}\n\ninterface DetailFieldProps {\n  label: string\n  value: React.ReactNode\n  className?: string\n}\n\nexport function DetailField({ label, value, className }: DetailFieldProps) {\n  const fieldId = React.useId()\n\n  return (\n    <div className={cn(\"space-y-1 group hover:bg-muted/30 rounded-md p-2 -m-2 transition-colors duration-200\", className)}>\n      <label\n        id={`${fieldId}-label`}\n        className=\"text-sm font-medium text-muted-foreground transition-colors duration-200 group-hover:text-foreground\"\n      >\n        {label}\n      </label>\n      <div\n        className=\"text-sm text-foreground transition-all duration-200\"\n        aria-labelledby={`${fieldId}-label`}\n        role=\"text\"\n      >\n        {value || (\n          <span className=\"text-muted-foreground italic\" aria-label={`${label} not provided`}>\n            Not provided\n          </span>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAQA;AACA;AAbA;;;;;;;AA2BA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAEO,SAAS,eAAe,EAC7B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,MAAM,EACN,SAAS,EACT,OAAO,IAAI,EACX,UAAU,KAAK,EACK;IACpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB,8CACA;YAEF,mBAAgB;YAChB,oBAAkB,cAAc,uBAAuB;YACvD,MAAK;YACL,cAAW;;8BAEX,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BACV,IAAG;4BACH,WAAU;;gCAET,yBACC,8OAAC,iNAAA,CAAA,UAAO;oCACN,WAAU;oCACV,eAAY;;;;;;gCAGf;;;;;;;wBAEF,6BACC,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,IAAG;sCACnB;;;;;;;;;;;;8BAKP,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,UAAU,CAAC;8BAEV;;;;;;gBAGF,wBACC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,cAAW;8BACnC;;;;;;;;;;;;;;;;;AAMb;AASO,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACvF,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;;0BACvF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,6BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;AAWO,SAAS,UAAU,EACxB,KAAK,EACL,WAAW,EACX,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,SAAS,EACM;IACf,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC1B,MAAM,UAAU,QAAQ,GAAG,QAAQ,MAAM,CAAC,GAAG;IAC7C,MAAM,gBAAgB,cAAc,GAAG,QAAQ,YAAY,CAAC,GAAG;IAE/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;0BACrF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;4BAET;4BACA,0BACC;;kDACE,8OAAC;wCAAK,WAAU;wCAAwB,eAAY;kDAAO;;;;;;kDAC3D,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;oBAI/B,6BACC,8OAAC;wBACC,IAAI;wBACJ,WAAU;kCAET;;;;;;;;;;;;0BAIN,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAqC;gBACvD,IAAI;gBACJ,oBAAoB;oBAAC;oBAAe;iBAAQ,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;gBAC1E,gBAAgB,CAAC,CAAC;gBAClB,iBAAiB;YACnB;YACC,uBACC,8OAAC;gBACC,IAAI;gBACJ,WAAU;gBACV,MAAK;gBACL,aAAU;0BAET;;;;;;;;;;;;AAKX;AAeO,SAAS,cAAc,EAC5B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,SAAS,EACT,QAAQ,EACR,UAAU,SAAS,EACnB,UAAU,KAAK,EACI;IACnB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ;QACF,OAAO;YACL,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,mBAAgB;YAChB,oBAAiB;YACjB,MAAK;YACL,cAAW;;8BAEX,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,IAAG;sCACb;;;;;;sCAEH,8OAAC,kIAAA,CAAA,oBAAiB;4BAChB,IAAG;4BACH,WAAU;sCAET;;;;;;;;;;;;8BAIL,8OAAC,kIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,cAAW;;sCACpC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,cAAY,GAAG,WAAW,YAAY,CAAC;sCAEtC;;;;;;sCAEH,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAS;4BACT,UAAU;4BACV,cAAY,GAAG,YAAY,YAAY,CAAC;4BACxC,SAAS;;gCAER,yBACC,8OAAC,iNAAA,CAAA,UAAO;oCACN,WAAU;oCACV,eAAY;;;;;;gCAGf;;;;;;;;;;;;;;;;;;;;;;;;AAMb;AAYO,SAAS,aAAa,EAC3B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,KAAK,EACM;IAClB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB;YAEF,mBAAgB;YAChB,oBAAkB,WAAW,2BAA2B;YACxD,MAAK;YACL,cAAW;;8BAEX,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,IAAG;sCACb;;;;;;wBAEF,0BACC,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,IAAG;sCACnB;;;;;;;;;;;;8BAKP,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,UAAU,CAAC;oBACX,cAAW;8BAEV;;;;;;gBAGF,yBACC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,cAAW;8BACnC;;;;;;;;;;;;;;;;;AAMb;AASO,SAAS,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,EAAsB;IAC3F,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC5B,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC3B,mBAAiB,GAAG,UAAU,MAAM,CAAC;;0BAErC,8OAAC;gBACC,IAAI,GAAG,UAAU,MAAM,CAAC;gBACxB,WAAU;0BAET;;;;;;0BAEH,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uEAAuE,WAAW,CAAC,QAAQ;gBACzG,MAAK;gBACL,mBAAiB,GAAG,UAAU,MAAM,CAAC;0BAEpC;;;;;;;;;;;;AAIT;AAQO,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAoB;IACvE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAE1B,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wFAAwF;;0BACzG,8OAAC;gBACC,IAAI,GAAG,QAAQ,MAAM,CAAC;gBACtB,WAAU;0BAET;;;;;;0BAEH,8OAAC;gBACC,WAAU;gBACV,mBAAiB,GAAG,QAAQ,MAAM,CAAC;gBACnC,MAAK;0BAEJ,uBACC,8OAAC;oBAAK,WAAU;oBAA+B,cAAY,GAAG,MAAM,aAAa,CAAC;8BAAE;;;;;;;;;;;;;;;;;AAO9F", "debugId": null}}, {"offset": {"line": 3275, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3300, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/rich-text-editor.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from 'react'\nimport { useE<PERSON>or, EditorContent } from '@tiptap/react'\nimport StarterKit from '@tiptap/starter-kit'\nimport Link from '@tiptap/extension-link'\nimport { default as UnderlineExtension } from '@tiptap/extension-underline'\nimport Placeholder from '@tiptap/extension-placeholder'\nimport {\n  Bold,\n  Italic,\n  Underline,\n  List,\n  ListOrdered,\n  Quote,\n  Undo,\n  Redo,\n  Link as LinkIcon,\n  Code,\n  Heading1,\n  Heading2,\n  Heading3,\n  AlignLeft,\n  AlignCenter,\n  AlignRight,\n  X\n} from 'lucide-react'\nimport { Button } from './button'\nimport { cn } from '@/lib/utils'\n\ninterface RichTextEditorProps {\n  content: string\n  onChange: (content: string) => void\n  placeholder?: string\n  className?: string\n  disabled?: boolean\n}\n\nconst MenuBar = ({ editor }: { editor: any }) => {\n  const [isUpdating, setIsUpdating] = React.useState(false)\n\n  // Force re-render when editor state changes\n  React.useEffect(() => {\n    if (!editor) return\n\n    const updateHandler = () => {\n      setIsUpdating(prev => !prev)\n    }\n\n    editor.on('selectionUpdate', updateHandler)\n    editor.on('transaction', updateHandler)\n\n    return () => {\n      editor.off('selectionUpdate', updateHandler)\n      editor.off('transaction', updateHandler)\n    }\n  }, [editor])\n\n  if (!editor) {\n    return null\n  }\n\n  const addLink = () => {\n    const previousUrl = editor.getAttributes('link').href\n    const url = window.prompt('Enter URL:', previousUrl)\n\n    // cancelled\n    if (url === null) {\n      return\n    }\n\n    // empty\n    if (url === '') {\n      editor.chain().focus().extendMarkRange('link').unsetLink().run()\n      return\n    }\n\n    // update link\n    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()\n  }\n\n  const removeLink = () => {\n    editor.chain().focus().unsetLink().run()\n  }\n\n  return (\n    <div className=\"border-b border-border p-2 flex flex-wrap gap-1 bg-muted/20\">\n      {/* Text Formatting */}\n      <div className=\"flex gap-1 border-r border-border pr-2 mr-2\">\n        <Button\n          type=\"button\"\n          variant={editor.isActive('bold') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Bold (Ctrl+B)\"\n        >\n          <Bold className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('italic') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Italic (Ctrl+I)\"\n        >\n          <Italic className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('underline') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleUnderline().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Underline (Ctrl+U)\"\n        >\n          <Underline className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('code') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleCode().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Code\"\n        >\n          <Code className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      {/* Headings */}\n      <div className=\"flex gap-1 border-r border-border pr-2 mr-2\">\n        <Button\n          type=\"button\"\n          variant={editor.isActive('heading', { level: 1 }) ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Heading 1\"\n        >\n          <Heading1 className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('heading', { level: 2 }) ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Heading 2\"\n        >\n          <Heading2 className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('heading', { level: 3 }) ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Heading 3\"\n        >\n          <Heading3 className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      {/* Lists */}\n      <div className=\"flex gap-1 border-r border-border pr-2 mr-2\">\n        <Button\n          type=\"button\"\n          variant={editor.isActive('bulletList') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Bullet List\"\n        >\n          <List className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('orderedList') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Numbered List\"\n        >\n          <ListOrdered className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant={editor.isActive('blockquote') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={() => editor.chain().focus().toggleBlockquote().run()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Blockquote\"\n        >\n          <Quote className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      {/* Links */}\n      <div className=\"flex gap-1 border-r border-border pr-2 mr-2\">\n        <Button\n          type=\"button\"\n          variant={editor.isActive('link') ? 'default' : 'ghost'}\n          size=\"sm\"\n          onClick={addLink}\n          className=\"h-8 w-8 p-0\"\n          title=\"Add/Edit Link\"\n        >\n          <LinkIcon className=\"h-4 w-4\" />\n        </Button>\n        {editor.isActive('link') && (\n          <Button\n            type=\"button\"\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={removeLink}\n            className=\"h-8 w-8 p-0\"\n            title=\"Remove Link\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        )}\n      </div>\n\n      {/* Undo/Redo */}\n      <div className=\"flex gap-1\">\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().undo().run()}\n          disabled={!editor.can().undo()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Undo (Ctrl+Z)\"\n        >\n          <Undo className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => editor.chain().focus().redo().run()}\n          disabled={!editor.can().redo()}\n          className=\"h-8 w-8 p-0\"\n          title=\"Redo (Ctrl+Y)\"\n        >\n          <Redo className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n\nexport function RichTextEditor({\n  content,\n  onChange,\n  placeholder = \"Start typing...\",\n  className,\n  disabled = false\n}: RichTextEditorProps) {\n  const [isInitialized, setIsInitialized] = React.useState(false)\n\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      UnderlineExtension,\n      Link.configure({\n        openOnClick: false,\n        HTMLAttributes: {\n          class: 'text-primary underline underline-offset-2 hover:text-primary/80',\n        },\n      }),\n      Placeholder.configure({\n        placeholder,\n      }),\n    ],\n    content,\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML())\n    },\n    onCreate: ({ editor }) => {\n      setIsInitialized(true)\n    },\n    onSelectionUpdate: ({ editor }) => {\n      // Force toolbar re-render on selection changes\n    },\n    editable: !disabled,\n    immediatelyRender: false,\n  })\n\n  // Only sync content when not actively editing\n  React.useEffect(() => {\n    if (editor && isInitialized && !editor.isFocused && content !== editor.getHTML()) {\n      editor.commands.setContent(content, false)\n    }\n  }, [content, editor, isInitialized])\n\n  return (\n    <div className={cn(\n      \"border border-input rounded-md overflow-hidden bg-background transition-colors\",\n      \"focus-within:border-ring focus-within:ring-1 focus-within:ring-ring/20\",\n      disabled && \"opacity-50 cursor-not-allowed\",\n      className\n    )}>\n      <MenuBar editor={editor} />\n      <EditorContent\n        editor={editor}\n        className={cn(\n          \"prose prose-sm max-w-none p-4 min-h-[120px] focus-within:outline-none\",\n          \"[&_.ProseMirror]:outline-none [&_.ProseMirror]:min-h-[120px]\",\n          \"[&_.ProseMirror]:transition-all [&_.ProseMirror]:duration-200\",\n          \"[&_.ProseMirror_p.is-editor-empty:first-child::before]:content-[attr(data-placeholder)]\",\n          \"[&_.ProseMirror_p.is-editor-empty:first-child::before]:text-muted-foreground\",\n          \"[&_.ProseMirror_p.is-editor-empty:first-child::before]:float-left\",\n          \"[&_.ProseMirror_p.is-editor-empty:first-child::before]:pointer-events-none\",\n          \"[&_.ProseMirror_p.is-editor-empty:first-child::before]:h-0\"\n        )}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AA5BA;;;;;;;;;;;AAsCA,MAAM,UAAU,CAAC,EAAE,MAAM,EAAmB;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,4CAA4C;IAC5C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAgB;YACpB,cAAc,CAAA,OAAQ,CAAC;QACzB;QAEA,OAAO,EAAE,CAAC,mBAAmB;QAC7B,OAAO,EAAE,CAAC,eAAe;QAEzB,OAAO;YACL,OAAO,GAAG,CAAC,mBAAmB;YAC9B,OAAO,GAAG,CAAC,eAAe;QAC5B;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,UAAU;QACd,MAAM,cAAc,OAAO,aAAa,CAAC,QAAQ,IAAI;QACrD,MAAM,MAAM,OAAO,MAAM,CAAC,cAAc;QAExC,YAAY;QACZ,IAAI,QAAQ,MAAM;YAChB;QACF;QAEA,QAAQ;QACR,IAAI,QAAQ,IAAI;YACd,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC,QAAQ,SAAS,GAAG,GAAG;YAC9D;QACF;QAEA,cAAc;QACd,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,CAAC,QAAQ,OAAO,CAAC;YAAE,MAAM;QAAI,GAAG,GAAG;IAC3E;IAEA,MAAM,aAAa;QACjB,OAAO,KAAK,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG;IACxC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,UAAU,YAAY;wBAC/C,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,YAAY,YAAY;wBACjD,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,eAAe,YAAY;wBACpD,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,GAAG,GAAG;wBAC3D,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,4MAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,UAAU,YAAY;wBAC/C,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,YAAY;wBAChE,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,YAAY;wBAChE,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAAK,YAAY;wBAChE,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,gBAAgB,YAAY;wBACrD,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,iBAAiB,YAAY;wBACtD,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,gBAAgB,YAAY;wBACrD,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,OAAO,QAAQ,CAAC,UAAU,YAAY;wBAC/C,MAAK;wBACL,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;;;;;;oBAErB,OAAO,QAAQ,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1B;AAEO,SAAS,eAAe,EAC7B,OAAO,EACP,QAAQ,EACR,cAAc,iBAAiB,EAC/B,SAAS,EACT,WAAW,KAAK,EACI;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEzD,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,2JAAA,CAAA,UAAU;YACV,mKAAA,CAAA,UAAkB;YAClB,8JAAA,CAAA,UAAI,CAAC,SAAS,CAAC;gBACb,aAAa;gBACb,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,qKAAA,CAAA,UAAW,CAAC,SAAS,CAAC;gBACpB;YACF;SACD;QACD;QACA,UAAU,CAAC,EAAE,MAAM,EAAE;YACnB,SAAS,OAAO,OAAO;QACzB;QACA,UAAU,CAAC,EAAE,MAAM,EAAE;YACnB,iBAAiB;QACnB;QACA,mBAAmB,CAAC,EAAE,MAAM,EAAE;QAC5B,+CAA+C;QACjD;QACA,UAAU,CAAC;QACX,mBAAmB;IACrB;IAEA,8CAA8C;IAC9C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU,iBAAiB,CAAC,OAAO,SAAS,IAAI,YAAY,OAAO,OAAO,IAAI;YAChF,OAAO,QAAQ,CAAC,UAAU,CAAC,SAAS;QACtC;IACF,GAAG;QAAC;QAAS;QAAQ;KAAc;IAEnC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kFACA,0EACA,YAAY,iCACZ;;0BAEA,8OAAC;gBAAQ,QAAQ;;;;;;0BACjB,8OAAC,kKAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,gEACA,iEACA,2FACA,gFACA,qEACA,8EACA;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 3782, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3807, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,sJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,sJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 3829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-1/admin/src/app/dashboard/products/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { ProtectedRoute } from \"@/components/auth/protected-route\";\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Table, \n  TableBody, \n  TableCell, \n  TableHead, \n  TableHeader, \n  TableRow \n} from \"@/components/ui/table\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport {\n  Modal,\n  ModalContent,\n  ModalDescription,\n  <PERSON><PERSON><PERSON>ooter,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>dalTitle,\n} from \"@/components/ui/modal\";\nimport {\n  EnhancedDialog,\n  FormSection,\n  FormField,\n} from \"@/components/ui/enhanced-dialog\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { RichTextEditor } from \"@/components/ui/rich-text-editor\";\nimport {\n  Search,\n  MoreHorizontal,\n  Eye,\n  Edit,\n  Star,\n  Package,\n  Plus,\n  Download,\n  RefreshCw,\n  Loader2,\n  Upload,\n  X,\n  Image as ImageIcon,\n  Link,\n  Camera,\n  Trash2\n} from \"lucide-react\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../../../convex/_generated/api\";\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { toast } from \"sonner\";\n\nexport default function ProductsPage() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [statusFilter, setStatusFilter] = useState<string>(\"all\");\n  const [currentPage, setCurrentPage] = useState(0);\n  const [showAddDialog, setShowAddDialog] = useState(false);\n  const [showViewDialog, setShowViewDialog] = useState(false);\n  const [showEditDialog, setShowEditDialog] = useState(false);\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState<any>(null);\n  const [productToDelete, setProductToDelete] = useState<any>(null);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [isExporting, setIsExporting] = useState(false);\n  const [isCreatingProduct, setIsCreatingProduct] = useState(false);\n  const [isUpdatingProduct, setIsUpdatingProduct] = useState(false);\n  const [isDeletingProduct, setIsDeletingProduct] = useState(false);\n  const pageSize = 20;\n\n  // Add Product Form State\n  const [newProduct, setNewProduct] = useState({\n    title: \"\",\n    description: \"\",\n    casNumber: \"\",\n    molecularFormula: \"\",\n    molecularWeight: \"\",\n    purity: \"\",\n    hsnNumber: \"\",\n    appearance: \"\",\n    solubility: \"\",\n    phValue: \"\",\n    chemicalName: \"\",\n    packaging: \"\",\n    features: \"\",\n    applications: \"\",\n    status: \"active\",\n    featured: false,\n    images: [] as string[],\n    collections: [] as string[], // Array of collection IDs\n  });\n\n  // Edit Product Form State\n  const [editProduct, setEditProduct] = useState({\n    title: \"\",\n    description: \"\",\n    casNumber: \"\",\n    molecularFormula: \"\",\n    molecularWeight: \"\",\n    purity: \"\",\n    hsnNumber: \"\",\n    appearance: \"\",\n    solubility: \"\",\n    phValue: \"\",\n    chemicalName: \"\",\n    packaging: \"\",\n    features: \"\",\n    applications: \"\",\n    status: \"active\",\n    featured: false,\n    images: [] as string[],\n    collections: [] as string[], // Array of collection IDs\n  });\n\n  // Image upload states\n  const [uploadingImages, setUploadingImages] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n\n  const { admin } = useAuth();\n\n  // Image compression utility\n  const compressImage = (file: File, maxWidth: number = 800, quality: number = 0.8): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        // Calculate new dimensions\n        let { width, height } = img;\n        if (width > maxWidth) {\n          height = (height * maxWidth) / width;\n          width = maxWidth;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        // Draw and compress\n        ctx?.drawImage(img, 0, 0, width, height);\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\n\n        // Check if compressed size is reasonable (< 200KB as base64)\n        if (compressedDataUrl.length > 200 * 1024 * 4/3) { // base64 is ~4/3 larger\n          reject(new Error('Image is still too large after compression'));\n        } else {\n          resolve(compressedDataUrl);\n        }\n      };\n\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  };\n\n  // Image upload handlers\n  const handleFileUpload = async (files: FileList) => {\n    setUploadingImages(true);\n    try {\n      const uploadPromises = Array.from(files).map(async (file) => {\n        // Validate file type\n        if (!file.type.startsWith('image/')) {\n          throw new Error(`${file.name} is not an image file`);\n        }\n\n        // Validate file size (max 2MB for better compression)\n        if (file.size > 2 * 1024 * 1024) {\n          throw new Error(`${file.name} is too large (max 2MB)`);\n        }\n\n        // Compress and convert to data URL\n        try {\n          return await compressImage(file);\n        } catch (compressionError) {\n          throw new Error(`${file.name}: ${compressionError instanceof Error ? compressionError.message : 'Compression failed'}`);\n        }\n      });\n\n      const imageUrls = await Promise.all(uploadPromises);\n\n      // Check total data size before adding\n      const currentDataSize = JSON.stringify(newProduct).length;\n      const newDataSize = JSON.stringify(imageUrls).length;\n      const totalSize = currentDataSize + newDataSize;\n\n      // Warn if approaching Convex limit (1MB = ~1,000,000 characters)\n      if (totalSize > 800000) { // 800KB warning threshold\n        throw new Error('Total product data would be too large. Please use fewer or smaller images.');\n      }\n\n      setNewProduct(prev => ({\n        ...prev,\n        images: [...prev.images, ...imageUrls]\n      }));\n\n      toast.success(`${imageUrls.length} image(s) uploaded and compressed successfully`);\n    } catch (error) {\n      toast.error(error instanceof Error ? error.message : 'Failed to upload images');\n    } finally {\n      setUploadingImages(false);\n    }\n  };\n\n  const handleUrlImport = (url: string) => {\n    if (!url.trim()) return;\n\n    // Basic URL validation\n    try {\n      new URL(url);\n      setNewProduct(prev => ({\n        ...prev,\n        images: [...prev.images, url.trim()]\n      }));\n      toast.success('Image URL added successfully');\n    } catch {\n      toast.error('Please enter a valid URL');\n    }\n  };\n\n  const removeImage = (index: number) => {\n    setNewProduct(prev => ({\n      ...prev,\n      images: prev.images.filter((_, i) => i !== index)\n    }));\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragActive(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragActive(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragActive(false);\n\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      handleFileUpload(files);\n    }\n  };\n\n  // Queries\n  const products = useQuery(api.products.getProducts, {\n    search: searchTerm || undefined,\n    status: statusFilter === \"all\" ? undefined : statusFilter as any,\n    limit: pageSize,\n    offset: currentPage * pageSize,\n  });\n\n  const productStats = useQuery(api.products.getProductStats);\n  const activeCollections = useQuery(api.collections.getActiveCollections);\n\n  // Mutations\n  const toggleFeatured = useMutation(api.products.toggleProductFeatured);\n  const updateStatus = useMutation(api.products.updateProductStatus);\n  const createProduct = useMutation(api.products.createProduct);\n  const upsertProduct = useMutation(api.products.upsertProduct);\n  const deleteProduct = useMutation(api.products.deleteProduct);\n  const getOrCreateDemoAdmin = useMutation(api.admins.getOrCreateDemoAdmin);\n  const createSampleCollections = useMutation(api.collections.createSampleCollections);\n\n  // Initialize collections if none exist\n  const initializeCollections = async () => {\n    if (!admin) return;\n\n    try {\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n      await createSampleCollections({ adminId });\n      toast.success(\"Sample collections created successfully\");\n    } catch (error) {\n      console.error(\"Failed to create sample collections:\", error);\n    }\n  };\n\n  const handleToggleFeatured = async (productId: string) => {\n    if (!admin) return;\n\n    try {\n      // Get or create the demo admin in Convex\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await toggleFeatured({\n        productId: productId as any,\n        adminId: adminId,\n      });\n      toast.success(\"Product featured status updated\");\n    } catch (error) {\n      toast.error(\"Failed to update product\");\n      console.error(error);\n    }\n  };\n\n  const handleUpdateStatus = async (productId: string, status: string) => {\n    if (!admin) return;\n\n    try {\n      // Get or create the demo admin in Convex\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await updateStatus({\n        productId: productId as any,\n        status: status as any,\n        adminId: adminId,\n      });\n      toast.success(\"Product status updated\");\n    } catch (error) {\n      toast.error(\"Failed to update product status\");\n      console.error(error);\n    }\n  };\n\n  // Handle opening delete confirmation dialog\n  const handleDeleteProduct = (product: any) => {\n    setProductToDelete(product);\n    setShowDeleteDialog(true);\n  };\n\n  // Handle confirming product deletion\n  const confirmDeleteProduct = async () => {\n    if (!admin || !productToDelete) return;\n\n    setIsDeletingProduct(true);\n    try {\n      // Get or create the demo admin in Convex\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await deleteProduct({\n        productId: productToDelete._id,\n        adminId: adminId,\n      });\n\n      toast.success(\"Product deleted successfully\");\n      setShowDeleteDialog(false);\n      setProductToDelete(null);\n\n      // Close other dialogs if they're showing the deleted product\n      if (selectedProduct?._id === productToDelete._id) {\n        setShowViewDialog(false);\n        setShowEditDialog(false);\n        setSelectedProduct(null);\n      }\n    } catch (error) {\n      toast.error(error instanceof Error ? error.message : \"Failed to delete product\");\n      console.error(error);\n    } finally {\n      setIsDeletingProduct(false);\n    }\n  };\n\n  // Handle opening view details modal\n  const handleViewProduct = (product: any) => {\n    setSelectedProduct(product);\n    setShowViewDialog(true);\n  };\n\n  // Handle opening edit product modal\n  const handleEditProduct = (product: any) => {\n    setSelectedProduct(product);\n\n    // Populate edit form with product data\n    setEditProduct({\n      title: product.title || \"\",\n      description: product.description || \"\",\n      casNumber: product.casNumber || \"\",\n      molecularFormula: product.molecularFormula || \"\",\n      molecularWeight: product.molecularWeight || \"\",\n      purity: product.purity || \"\",\n      hsnNumber: product.hsnNumber || \"\",\n      appearance: product.appearance || \"\",\n      solubility: product.solubility || \"\",\n      phValue: product.phValue || \"\",\n      chemicalName: product.chemicalName || \"\",\n      packaging: product.packaging || \"\",\n      features: product.features ? product.features.join(\", \") : \"\",\n      applications: product.applications ? product.applications.join(\", \") : \"\",\n      status: product.status || \"active\",\n      featured: product.featured || false,\n      images: product.images ? product.images.map((img: any) => img.url) : [],\n      collections: product.collections || [],\n    });\n\n    setShowEditDialog(true);\n  };\n\n  // Handle updating product\n  const handleUpdateProduct = async () => {\n    if (!admin || !selectedProduct) return;\n\n    setIsUpdatingProduct(true);\n    try {\n      // Enhanced validation\n      if (!editProduct.title.trim()) {\n        toast.error(\"Product title is required\");\n        setIsUpdatingProduct(false);\n        return;\n      }\n\n      // Check if description has meaningful content (not just empty HTML tags)\n      const descriptionText = editProduct.description.replace(/<[^>]*>/g, '').trim();\n      if (!descriptionText) {\n        toast.error(\"Product description is required\");\n        setIsUpdatingProduct(false);\n        return;\n      }\n\n      // Validate image URLs and data size\n      const invalidImages = editProduct.images.filter(img => {\n        if (!img.trim()) return false;\n        // Skip validation for data URLs (base64 images)\n        if (img.startsWith('data:')) return false;\n        try {\n          new URL(img);\n          return false;\n        } catch {\n          return true;\n        }\n      });\n\n      if (invalidImages.length > 0) {\n        toast.error(\"Please check image URLs - some appear to be invalid\");\n        setIsUpdatingProduct(false);\n        return;\n      }\n\n      // Prepare product data for update\n      const productData = {\n        productId: selectedProduct.productId || selectedProduct._id,\n        title: editProduct.title.trim(),\n        description: editProduct.description,\n        descriptionHtml: editProduct.description, // For rich text content\n        casNumber: editProduct.casNumber.trim() || undefined,\n        molecularFormula: editProduct.molecularFormula.trim() || undefined,\n        molecularWeight: editProduct.molecularWeight.trim() || undefined,\n        purity: editProduct.purity || undefined,\n        hsnNumber: editProduct.hsnNumber.trim() || undefined,\n        appearance: editProduct.appearance.trim() || undefined,\n        solubility: editProduct.solubility.trim() || undefined,\n        phValue: editProduct.phValue || undefined,\n        chemicalName: editProduct.chemicalName.trim() || undefined,\n        packaging: editProduct.packaging.trim() || undefined,\n        features: editProduct.features.trim() ? editProduct.features.split(',').map(f => f.trim()) : undefined,\n        applications: editProduct.applications.trim() ? editProduct.applications.split(',').map(a => a.trim()) : undefined,\n        collections: editProduct.collections, // Array of collection IDs\n        images: editProduct.images\n          .filter(img => img.trim())\n          .map(url => ({ url: url.trim() })),\n        status: editProduct.status as any,\n        featured: editProduct.featured,\n        tags: [], // Add tags if needed\n        // Include priceRange from existing product or default values\n        priceRange: selectedProduct.priceRange || {\n          minVariantPrice: { amount: \"0\", currencyCode: \"INR\" },\n          maxVariantPrice: { amount: \"0\", currencyCode: \"INR\" },\n        },\n      };\n\n      // Check total data size before submission\n      const dataSize = JSON.stringify(productData).length;\n      if (dataSize > 900000) { // 900KB limit (leaving buffer for Convex overhead)\n        toast.error(\"Product data is too large. Please reduce image sizes or use fewer images.\");\n        setIsUpdatingProduct(false);\n        return;\n      }\n\n      // Get or create the demo admin in Convex\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await upsertProduct({\n        ...productData,\n        adminId: adminId,\n      });\n\n      toast.success(\"Product updated successfully\");\n      setShowEditDialog(false);\n      setSelectedProduct(null);\n\n    } catch (error) {\n      toast.error(\"Failed to update product\");\n      console.error(error);\n    } finally {\n      setIsUpdatingProduct(false);\n    }\n  };\n\n  const handleAddProduct = async () => {\n    if (!admin) return;\n\n    setIsCreatingProduct(true);\n    try {\n      // Enhanced validation\n      if (!newProduct.title.trim()) {\n        toast.error(\"Product title is required\");\n        setIsCreatingProduct(false);\n        return;\n      }\n\n      // Check if description has meaningful content (not just empty HTML tags)\n      const descriptionText = newProduct.description.replace(/<[^>]*>/g, '').trim();\n      if (!descriptionText) {\n        toast.error(\"Product description is required\");\n        setIsCreatingProduct(false);\n        return;\n      }\n\n      // Validate image URLs and data size\n      const invalidImages = newProduct.images.filter(img => {\n        if (!img.trim()) return false;\n        // Skip validation for data URLs (base64 images)\n        if (img.startsWith('data:')) return false;\n        try {\n          new URL(img);\n          return false;\n        } catch {\n          return true;\n        }\n      });\n\n      if (invalidImages.length > 0) {\n        toast.error(\"Please check image URLs - some appear to be invalid\");\n        setIsCreatingProduct(false);\n        return;\n      }\n\n      // Check total data size before submission\n      const productData = {\n        productId: `prod_${Date.now()}`,\n        title: newProduct.title.trim(),\n        description: newProduct.description,\n        casNumber: newProduct.casNumber.trim() || undefined,\n        molecularFormula: newProduct.molecularFormula.trim() || undefined,\n        molecularWeight: newProduct.molecularWeight.trim() || undefined,\n        purity: newProduct.purity || undefined,\n        hsnNumber: newProduct.hsnNumber.trim() || undefined,\n        appearance: newProduct.appearance.trim() || undefined,\n        solubility: newProduct.solubility.trim() || undefined,\n        phValue: newProduct.phValue || undefined,\n        chemicalName: newProduct.chemicalName.trim() || undefined,\n        packaging: newProduct.packaging.trim() || undefined,\n        features: newProduct.features.trim() ? newProduct.features.split(',').map(f => f.trim()) : undefined,\n        applications: newProduct.applications.trim() ? newProduct.applications.split(',').map(a => a.trim()) : undefined,\n        collections: newProduct.collections, // Array of collection IDs\n        images: newProduct.images\n          .filter(img => img.trim())\n          .map(url => ({ url: url.trim() })),\n        status: newProduct.status as any,\n        featured: newProduct.featured,\n      };\n\n      const dataSize = JSON.stringify(productData).length;\n      if (dataSize > 900000) { // 900KB limit (leaving buffer for Convex overhead)\n        toast.error(\"Product data is too large. Please reduce image sizes or use fewer images.\");\n        setIsCreatingProduct(false);\n        return;\n      }\n\n      // Get or create the demo admin in Convex\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await createProduct({\n        ...productData,\n        adminId: adminId,\n      });\n\n      toast.success(\"Product created successfully\");\n      setShowAddDialog(false);\n\n      // Reset form and upload states\n      setNewProduct({\n        title: \"\",\n        description: \"\", // Will be empty HTML\n        casNumber: \"\",\n        molecularFormula: \"\",\n        molecularWeight: \"\",\n        purity: \"\",\n        hsnNumber: \"\",\n        appearance: \"\",\n        solubility: \"\",\n        phValue: \"\",\n        chemicalName: \"\",\n        packaging: \"\",\n        features: \"\",\n        applications: \"\",\n        status: \"active\",\n        featured: false,\n        images: [],\n        collections: [],\n      });\n\n      // Reset upload states\n      setUploadingImages(false);\n      setDragActive(false);\n    } catch (error) {\n      toast.error(\"Failed to create product\");\n      console.error(error);\n    } finally {\n      setIsCreatingProduct(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setIsRefreshing(true);\n    try {\n      // Force refetch by updating search term temporarily\n      const currentSearch = searchTerm;\n      setSearchTerm(\"\");\n      setTimeout(() => {\n        setSearchTerm(currentSearch);\n        setIsRefreshing(false);\n        toast.success(\"Products refreshed\");\n      }, 500);\n    } catch (error) {\n      setIsRefreshing(false);\n      toast.error(\"Failed to refresh products\");\n    }\n  };\n\n  const handleExport = async () => {\n    if (!products || products.length === 0) {\n      toast.error(\"No products to export\");\n      return;\n    }\n\n    setIsExporting(true);\n    try {\n      // Prepare CSV data\n      const csvHeaders = [\n        \"Product ID\",\n        \"Title\",\n        \"Description\",\n        \"CAS Number\",\n        \"Molecular Formula\",\n        \"Molecular Weight\",\n        \"Purity\",\n        \"pH Value\",\n        \"Appearance\",\n        \"Solubility\",\n        \"Status\",\n        \"Featured\",\n        \"Created Date\",\n        \"Updated Date\"\n      ];\n\n      const csvData = products.map(product => [\n        product.productId || product._id,\n        product.title,\n        product.description || \"\",\n        product.casNumber || \"\",\n        product.molecularFormula || \"\",\n        product.molecularWeight || \"\",\n        product.purity || \"\",\n        product.phValue || \"\",\n        product.appearance || \"\",\n        product.solubility || \"\",\n        product.status,\n        product.featured ? \"Yes\" : \"No\",\n        formatDate(product.createdAt),\n        formatDate(product.updatedAt)\n      ]);\n\n      // Create CSV content\n      const csvContent = [\n        csvHeaders.join(\",\"),\n        ...csvData.map(row => row.map(cell => `\"${cell}\"`).join(\",\"))\n      ].join(\"\\n\");\n\n      // Create and download file\n      const blob = new Blob([csvContent], { type: \"text/csv;charset=utf-8;\" });\n      const link = document.createElement(\"a\");\n      const url = URL.createObjectURL(blob);\n      link.setAttribute(\"href\", url);\n      link.setAttribute(\"download\", `products_export_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = \"hidden\";\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      toast.success(\"Products exported successfully\");\n    } catch (error) {\n      toast.error(\"Failed to export products\");\n      console.error(error);\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"active\":\n        return <Badge variant=\"default\" className=\"bg-green-500\">Active</Badge>;\n      case \"inactive\":\n        return <Badge variant=\"secondary\">Inactive</Badge>;\n      case \"discontinued\":\n        return <Badge variant=\"destructive\">Discontinued</Badge>;\n      case \"pending_review\":\n        return <Badge variant=\"outline\" className=\"border-yellow-500 text-yellow-500\">Pending Review</Badge>;\n      default:\n        return <Badge variant=\"outline\">{status}</Badge>;\n    }\n  };\n\n\n\n  const formatDate = (timestamp: number) => {\n    return new Date(timestamp).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n    });\n  };\n\n  return (\n    <ProtectedRoute requiredPermission=\"products.read\">\n      <DashboardLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold tracking-tight\">Product Management</h1>\n              <p className=\"text-muted-foreground\">\n                Manage your product catalog and inventory\n              </p>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleExport}\n                disabled={isExporting || !products || products.length === 0}\n              >\n                <Download className={`h-4 w-4 mr-2 ${isExporting ? 'animate-spin' : ''}`} />\n                {isExporting ? 'Exporting...' : 'Export'}\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleRefresh}\n                disabled={isRefreshing}\n              >\n                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />\n                {isRefreshing ? 'Refreshing...' : 'Refresh'}\n              </Button>\n              <Button size=\"sm\" onClick={() => setShowAddDialog(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Product\n              </Button>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-5\">\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Total Products</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{productStats?.total || 0}</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Active</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-green-600\">{productStats?.active || 0}</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Featured</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-blue-600\">{productStats?.featured || 0}</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Inactive</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-gray-600\">{productStats?.inactive || 0}</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Collections</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-purple-600\">{productStats?.collections || 0}</div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Products Table */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Products</CardTitle>\n              <CardDescription>\n                Manage your product catalog\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center gap-4 mb-6\">\n                <div className=\"relative flex-1 max-w-sm\">\n                  <Search className={`absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 ${\n                    products && products.length > 0 ? 'text-muted-foreground' : 'text-muted-foreground/50'\n                  }`} />\n                  <Input\n                    placeholder=\"Search products...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className={`pl-10 ${\n                      products && products.length > 0 ? '' : 'opacity-50 cursor-not-allowed'\n                    }`}\n                    disabled={!products || products.length === 0}\n                  />\n                </div>\n                <Select\n                  value={statusFilter}\n                  onValueChange={setStatusFilter}\n                  disabled={!products || products.length === 0}\n                >\n                  <SelectTrigger className={`w-[180px] ${\n                    products && products.length > 0 ? '' : 'opacity-50 cursor-not-allowed'\n                  }`}>\n                    <SelectValue placeholder=\"Filter by status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All Status</SelectItem>\n                    <SelectItem value=\"active\">Active</SelectItem>\n                    <SelectItem value=\"inactive\">Inactive</SelectItem>\n                    <SelectItem value=\"discontinued\">Discontinued</SelectItem>\n                    <SelectItem value=\"pending_review\">Pending Review</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {!products ? (\n                // Skeleton loading state\n                <div className=\"rounded-md border\">\n                  <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead>Product</TableHead>\n                        <TableHead>Chemical Info</TableHead>\n                        <TableHead>Status</TableHead>\n                        <TableHead>Updated</TableHead>\n                        <TableHead className=\"text-right\">Actions</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {Array.from({ length: 5 }).map((_, index) => (\n                        <TableRow key={index}>\n                          <TableCell>\n                            <div className=\"flex items-center gap-3\">\n                              <Skeleton className=\"w-10 h-10 rounded\" />\n                              <div className=\"space-y-2\">\n                                <Skeleton className=\"h-4 w-32\" />\n                                <Skeleton className=\"h-3 w-20\" />\n                              </div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"space-y-2\">\n                              <Skeleton className=\"h-3 w-24\" />\n                              <Skeleton className=\"h-3 w-28\" />\n                              <Skeleton className=\"h-3 w-20\" />\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <Skeleton className=\"h-6 w-16 rounded-full\" />\n                          </TableCell>\n                          <TableCell>\n                            <Skeleton className=\"h-3 w-20\" />\n                          </TableCell>\n                          <TableCell className=\"text-right\">\n                            <Skeleton className=\"h-8 w-8 rounded ml-auto\" />\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </div>\n              ) : products.length === 0 ? (\n                // Empty state\n                <div className=\"rounded-md border border-dashed border-muted-foreground/25 bg-muted/5\">\n                  <div className=\"flex flex-col items-center justify-center py-16 px-8 text-center\">\n                    <div className=\"mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-6\">\n                      <Package className=\"h-8 w-8 text-muted-foreground\" />\n                    </div>\n\n                    <div className=\"space-y-3 mb-8\">\n                      <h3 className=\"text-xl font-semibold text-foreground\">\n                        No Chemical Products Available\n                      </h3>\n                      <p className=\"text-muted-foreground max-w-md mx-auto leading-relaxed\">\n                        Start building your chemical catalog by adding your first product.\n                        You can add detailed chemical properties, specifications, and organize products into collections.\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-4\">\n                      <Button\n                        onClick={() => setShowAddDialog(true)}\n                        className=\"px-6 py-2.5 text-sm font-medium\"\n                      >\n                        <Plus className=\"h-4 w-4 mr-2\" />\n                        Add New Product\n                      </Button>\n\n                      <div className=\"text-xs text-muted-foreground space-y-1\">\n                        <p>💡 <strong>Quick tip:</strong> You can also import products in bulk using CSV files</p>\n                        <p>🔬 Add chemical properties like CAS numbers, molecular formulas, and pH values</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                // Products table with data\n                <div className=\"rounded-md border\">\n                  <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead>Product</TableHead>\n                        <TableHead>Chemical Info</TableHead>\n                        <TableHead>Status</TableHead>\n                        <TableHead>Updated</TableHead>\n                        <TableHead className=\"text-right\">Actions</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {products.map((product) => (\n                      <TableRow key={product._id}>\n                        <TableCell>\n                          <div className=\"flex items-center gap-3\">\n                            {product.images && product.images.length > 0 ? (\n                              <img\n                                src={product.images[0].url}\n                                alt={product.title}\n                                className=\"w-10 h-10 rounded object-cover\"\n                              />\n                            ) : (\n                              <div className=\"w-10 h-10 bg-muted rounded flex items-center justify-center\">\n                                <Package className=\"h-5 w-5 text-muted-foreground\" />\n                              </div>\n                            )}\n                            <div>\n                              <div className=\"font-medium\">{product.title}</div>\n                              {product.featured && (\n                                <Badge variant=\"outline\" className=\"text-yellow-600 border-yellow-600 mt-1\">\n                                  <Star className=\"h-3 w-3 mr-1\" />\n                                  Featured\n                                </Badge>\n                              )}\n                            </div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"space-y-1\">\n                            {product.casNumber && (\n                              <div className=\"text-sm\">\n                                <span className=\"font-medium\">CAS:</span> {product.casNumber}\n                              </div>\n                            )}\n                            {product.molecularFormula && (\n                              <div className=\"text-sm\">\n                                <span className=\"font-medium\">Formula:</span> {product.molecularFormula}\n                              </div>\n                            )}\n                            {product.purity && (\n                              <div className=\"text-sm\">\n                                <span className=\"font-medium\">Purity:</span> {product.purity}\n                              </div>\n                            )}\n                            {product.phValue && (\n                              <div className=\"text-sm\">\n                                <span className=\"font-medium\">pH:</span> {product.phValue}\n                              </div>\n                            )}\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          {getStatusBadge(product.status)}\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            {formatDate(product.updatedAt)}\n                          </div>\n                        </TableCell>\n                        <TableCell className=\"text-right\">\n                          <DropdownMenu>\n                            <DropdownMenuTrigger asChild>\n                              <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n                                <MoreHorizontal className=\"h-4 w-4\" />\n                              </Button>\n                            </DropdownMenuTrigger>\n                            <DropdownMenuContent align=\"end\">\n                              <DropdownMenuLabel>Actions</DropdownMenuLabel>\n                              <DropdownMenuItem onClick={() => handleViewProduct(product)}>\n                                <Eye className=\"mr-2 h-4 w-4\" />\n                                View Details\n                              </DropdownMenuItem>\n                              <DropdownMenuItem onClick={() => handleEditProduct(product)}>\n                                <Edit className=\"mr-2 h-4 w-4\" />\n                                Edit Product\n                              </DropdownMenuItem>\n                              <DropdownMenuSeparator />\n                              <DropdownMenuItem\n                                onClick={() => handleToggleFeatured(product._id)}\n                              >\n                                <Star className=\"mr-2 h-4 w-4\" />\n                                {product.featured ? \"Remove from Featured\" : \"Mark as Featured\"}\n                              </DropdownMenuItem>\n                              <DropdownMenuSeparator />\n                              <DropdownMenuItem\n                                onClick={() => handleUpdateStatus(product._id, product.status === \"active\" ? \"inactive\" : \"active\")}\n                              >\n                                {product.status === \"active\" ? \"Deactivate\" : \"Activate\"}\n                              </DropdownMenuItem>\n                              <DropdownMenuSeparator />\n                              <DropdownMenuItem\n                                onClick={() => handleDeleteProduct(product)}\n                                className=\"text-red-600 focus:text-red-600 focus:bg-red-50\"\n                              >\n                                <Trash2 className=\"mr-2 h-4 w-4\" />\n                                Delete Product\n                              </DropdownMenuItem>\n                            </DropdownMenuContent>\n                          </DropdownMenu>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                    </TableBody>\n                  </Table>\n                </div>\n              )}\n\n              {/* Pagination - only show when there are products */}\n              {products && products.length > 0 && (\n                <div className=\"flex items-center justify-between mt-4\">\n                  <div className=\"text-sm text-muted-foreground\">\n                    Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, products.length)} of {products.length} products\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n                      disabled={currentPage === 0}\n                    >\n                      Previous\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => setCurrentPage(currentPage + 1)}\n                      disabled={products.length < pageSize}\n                    >\n                      Next\n                    </Button>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Add Product Modal */}\n          <Modal\n            open={showAddDialog}\n            onOpenChange={setShowAddDialog}\n            className=\"w-[95vw] max-w-7xl\"\n          >\n            <ModalContent\n              className=\"w-full max-h-[95vh] overflow-hidden flex flex-col\"\n              showCloseButton={true}\n              onClose={() => setShowAddDialog(false)}\n            >\n              <ModalHeader>\n                <ModalTitle className=\"flex items-center gap-2\">\n                  {isCreatingProduct && <Loader2 className=\"h-4 w-4 animate-spin\" />}\n                  Add New Product\n                </ModalTitle>\n                <ModalDescription>\n                  Create a new chemical product in your catalog\n                </ModalDescription>\n              </ModalHeader>\n\n              <div className=\"flex-1 overflow-y-auto px-8 py-6\">\n                {isCreatingProduct && !newProduct.title ? (\n                  // Skeleton loading for add modal initialization\n                  <div className=\"space-y-10\">\n                    {/* Basic Information Skeleton */}\n                    <div className=\"space-y-6\">\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-6 w-48\" />\n                        <Skeleton className=\"h-4 w-96\" />\n                      </div>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        <div className=\"space-y-2\">\n                          <Skeleton className=\"h-4 w-24\" />\n                          <Skeleton className=\"h-10 w-full\" />\n                        </div>\n                        <div className=\"space-y-2\">\n                          <Skeleton className=\"h-4 w-24\" />\n                          <Skeleton className=\"h-10 w-full\" />\n                        </div>\n                      </div>\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-4 w-24\" />\n                        <Skeleton className=\"h-32 w-full\" />\n                      </div>\n                    </div>\n\n                    {/* Image Upload Skeleton */}\n                    <div className=\"space-y-6\">\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-6 w-48\" />\n                        <Skeleton className=\"h-4 w-96\" />\n                      </div>\n                      <Skeleton className=\"h-48 w-full rounded-lg\" />\n                    </div>\n\n                    {/* Collections Skeleton */}\n                    <div className=\"space-y-6\">\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-6 w-48\" />\n                        <Skeleton className=\"h-4 w-96\" />\n                      </div>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                        {Array.from({ length: 6 }).map((_, index) => (\n                          <div key={index} className=\"border rounded-lg p-3\">\n                            <div className=\"flex items-center space-x-3\">\n                              <Skeleton className=\"h-4 w-4 rounded\" />\n                              <div className=\"flex-1 space-y-2\">\n                                <Skeleton className=\"h-4 w-3/4\" />\n                                <Skeleton className=\"h-3 w-1/2\" />\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-10\">\n                    {/* Basic Information */}\n                    <FormSection\n                      title=\"Basic Information\"\n                      description=\"Essential product details and identification\"\n                    >\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <FormField\n                      label=\"Product Title\"\n                      required\n                      description=\"The main name of your chemical product\"\n                    >\n                      <Input\n                        placeholder=\"Enter product title\"\n                        value={newProduct.title}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, title: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                      />\n                    </FormField>\n\n                    <FormField\n                      label=\"Chemical Name\"\n                      description=\"IUPAC or common chemical name\"\n                    >\n                      <Input\n                        placeholder=\"Enter chemical name\"\n                        value={newProduct.chemicalName}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, chemicalName: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                      />\n                    </FormField>\n                  </div>\n\n                  <FormField\n                    label=\"Description\"\n                    description=\"Detailed description of the product and its properties\"\n                  >\n                    <RichTextEditor\n                      content={newProduct.description}\n                      onChange={(content) => setNewProduct(prev => ({ ...prev, description: content }))}\n                      placeholder=\"Enter a detailed product description with formatting...\"\n                      className=\"min-h-[200px]\"\n                    />\n                  </FormField>\n                </FormSection>\n\n                {/* Product Images */}\n                <FormSection\n                  title=\"Product Images\"\n                  description=\"Upload or add images to showcase your chemical product\"\n                >\n                  <div className=\"space-y-6\">\n                    {/* Drag & Drop Upload Area */}\n                    <div\n                      className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all ${\n                        dragActive\n                          ? 'border-primary bg-primary/5'\n                          : 'border-gray-300 hover:border-gray-400'\n                      } ${uploadingImages ? 'opacity-50 pointer-events-none' : ''}`}\n                      onDragOver={handleDragOver}\n                      onDragLeave={handleDragLeave}\n                      onDrop={handleDrop}\n                    >\n                      <input\n                        type=\"file\"\n                        multiple\n                        accept=\"image/*\"\n                        onChange={(e) => e.target.files && handleFileUpload(e.target.files)}\n                        className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                        disabled={uploadingImages}\n                      />\n\n                      <div className=\"space-y-4\">\n                        <div className=\"mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\">\n                          {uploadingImages ? (\n                            <Loader2 className=\"h-6 w-6 animate-spin text-primary\" />\n                          ) : (\n                            <ImageIcon className=\"h-6 w-6 text-gray-400\" />\n                          )}\n                        </div>\n\n                        <div>\n                          <p className=\"text-lg font-medium text-gray-500\">\n                            {uploadingImages ? 'Uploading images...' : 'Drop images here or click to browse'}\n                          </p>\n                          <p className=\"text-sm text-gray-500 mt-1\">\n                            Supports JPG, PNG, WebP up to 2MB each (auto-compressed)\n                          </p>\n                        </div>\n\n                        <div className=\"flex items-center justify-center gap-4\">\n                          <Button\n                            type=\"button\"\n                            variant=\"outline\"\n                            size=\"sm\"\n                            disabled={uploadingImages}\n                            className=\"pointer-events-none\"\n                          >\n                            <Upload className=\"h-4 w-4 mr-2\" />\n                            Choose Files\n                          </Button>\n                          <span className=\"text-sm text-gray-400\">or</span>\n                          <Button\n                            type=\"button\"\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => {\n                              const url = prompt('Enter image URL:');\n                              if (url) handleUrlImport(url);\n                            }}\n                            disabled={uploadingImages}\n                          >\n                            <Link className=\"h-4 w-4 mr-2\" />\n                            Add URL\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Image Previews */}\n                    {newProduct.images.length > 0 && (\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <h4 className=\"text-sm font-medium text-gray-900\">\n                            Uploaded Images ({newProduct.images.length})\n                          </h4>\n                          <span className=\"text-xs text-gray-500\">\n                            Data size: {Math.round(JSON.stringify(newProduct.images).length / 1024)}KB\n                          </span>\n                        </div>\n                        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                          {newProduct.images.map((image, index) => (\n                            <div key={index} className=\"relative group\">\n                              <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100 border\">\n                                <img\n                                  src={image}\n                                  alt={`Product image ${index + 1}`}\n                                  className=\"w-full h-full object-cover\"\n                                  onError={(e) => {\n                                    const target = e.target as HTMLImageElement;\n                                    target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiAxNkM5Ljc5IDEzLjc5IDkuNzkgMTAuMjEgMTIgOEMxNC4yMSAxMC4yMSAxNC4yMSAxMy43OSAxMiAxNloiIGZpbGw9IiM5Q0E0QUYiLz4KPC9zdmc+';\n                                  }}\n                                />\n                              </div>\n                              <Button\n                                type=\"button\"\n                                variant=\"destructive\"\n                                size=\"sm\"\n                                className=\"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                onClick={() => removeImage(index)}\n                              >\n                                <X className=\"h-3 w-3\" />\n                              </Button>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* URL Import Section */}\n                    <div className=\"border rounded-lg p-4 bg-gray-50\">\n                      <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Add Image by URL</h4>\n                      <div className=\"flex gap-2\">\n                        <Input\n                          placeholder=\"https://example.com/image.jpg\"\n                          onKeyDown={(e) => {\n                            if (e.key === 'Enter') {\n                              const target = e.target as HTMLInputElement;\n                              handleUrlImport(target.value);\n                              target.value = '';\n                            }\n                          }}\n                          className=\"flex-1\"\n                        />\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={(e) => {\n                            const input = e.currentTarget.previousElementSibling as HTMLInputElement;\n                            handleUrlImport(input.value);\n                            input.value = '';\n                          }}\n                        >\n                          Add\n                        </Button>\n                      </div>\n                      <p className=\"text-xs text-gray-500 mt-2\">\n                        Enter a direct link to an image file\n                      </p>\n                    </div>\n                  </div>\n                </FormSection>\n\n                {/* Collections */}\n                <FormSection\n                  title=\"Product Collections\"\n                  description=\"Organize your product by assigning it to relevant collections\"\n                >\n                  <FormField\n                    label=\"Collections\"\n                    description=\"Select one or more collections for this product\"\n                  >\n                    <div className=\"space-y-4\">\n                      {activeCollections === undefined ? (\n                        // Skeleton loading for collections\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                          {Array.from({ length: 6 }).map((_, index) => (\n                            <div key={index} className=\"border rounded-lg p-3\">\n                              <div className=\"flex items-center space-x-3\">\n                                <Skeleton className=\"h-4 w-4 rounded\" />\n                                <div className=\"flex-1 space-y-2\">\n                                  <Skeleton className=\"h-4 w-3/4\" />\n                                  <Skeleton className=\"h-3 w-1/2\" />\n                                  <Skeleton className=\"h-3 w-1/3\" />\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : activeCollections && activeCollections.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                          {activeCollections.map((collection) => (\n                            <div\n                              key={collection._id}\n                              className={`border rounded-lg p-3 cursor-pointer transition-all hover:border-primary/50 ${\n                                newProduct.collections.includes(collection.collectionId)\n                                  ? 'border-primary bg-primary/5'\n                                  : 'border-gray-200'\n                              }`}\n                              onClick={() => {\n                                setNewProduct(prev => ({\n                                  ...prev,\n                                  collections: prev.collections.includes(collection.collectionId)\n                                    ? prev.collections.filter(id => id !== collection.collectionId)\n                                    : [...prev.collections, collection.collectionId]\n                                }));\n                              }}\n                            >\n                              <div className=\"flex items-center space-x-3\">\n                                <input\n                                  type=\"checkbox\"\n                                  checked={newProduct.collections.includes(collection.collectionId)}\n                                  onChange={() => {}} // Handled by parent div onClick\n                                  className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/20\"\n                                />\n                                <div className=\"flex-1 min-w-0\">\n                                  <p className=\"text-sm font-medium text-gray-100 truncate\">\n                                    {collection.title}\n                                  </p>\n                                  {collection.description && (\n                                    <p className=\"text-xs text-gray-500 truncate\">\n                                      {collection.description}\n                                    </p>\n                                  )}\n                                  <p className=\"text-xs text-gray-400\">\n                                    {collection.productCount || 0} products\n                                  </p>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-8 text-gray-500\">\n                          <Package className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                          <p className=\"text-sm\">No collections available</p>\n                          <p className=\"text-xs text-gray-400 mt-1 mb-4\">\n                            Create collections first to organize your products\n                          </p>\n                          <Button\n                            type=\"button\"\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={initializeCollections}\n                            className=\"text-xs\"\n                          >\n                            <Plus className=\"h-3 w-3 mr-1\" />\n                            Create Sample Collections\n                          </Button>\n                        </div>\n                      )}\n\n                      {newProduct.collections.length > 0 && (\n                        <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                          <p className=\"text-sm font-medium text-blue-900 mb-2\">\n                            Selected Collections ({newProduct.collections.length}):\n                          </p>\n                          <div className=\"flex flex-wrap gap-2\">\n                            {newProduct.collections.map((collectionId) => {\n                              const collection = activeCollections?.find(c => c.collectionId === collectionId);\n                              return collection ? (\n                                <span\n                                  key={collectionId}\n                                  className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\n                                >\n                                  {collection.title}\n                                  <button\n                                    type=\"button\"\n                                    className=\"ml-1.5 h-3 w-3 rounded-full inline-flex items-center justify-center text-blue-400 hover:bg-blue-200 hover:text-blue-500\"\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      setNewProduct(prev => ({\n                                        ...prev,\n                                        collections: prev.collections.filter(id => id !== collectionId)\n                                      }));\n                                    }}\n                                  >\n                                    <X className=\"h-2 w-2\" />\n                                  </button>\n                                </span>\n                              ) : null;\n                            })}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </FormField>\n                </FormSection>\n\n                {/* Chemical Properties */}\n                <FormSection\n                  title=\"Chemical Properties\"\n                  description=\"Technical specifications and chemical characteristics\"\n                >\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <FormField\n                      label=\"CAS Number\"\n                      description=\"Chemical Abstracts Service registry number\"\n                    >\n                      <Input\n                        placeholder=\"e.g., 64-17-5\"\n                        value={newProduct.casNumber}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, casNumber: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20 font-mono\"\n                      />\n                    </FormField>\n\n                    <FormField\n                      label=\"HSN Number\"\n                      description=\"Harmonized System of Nomenclature code\"\n                    >\n                      <Input\n                        placeholder=\"e.g., 29051100\"\n                        value={newProduct.hsnNumber}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, hsnNumber: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20 font-mono\"\n                      />\n                    </FormField>\n\n                    <FormField\n                      label=\"Purity\"\n                      description=\"Chemical purity percentage\"\n                    >\n                      <div className=\"space-y-2\">\n                        <Select\n                          value={newProduct.purity}\n                          onValueChange={(value) => {\n                            if (value === \"custom\") {\n                              setNewProduct(prev => ({ ...prev, purity: \"\" }));\n                            } else {\n                              setNewProduct(prev => ({ ...prev, purity: value }));\n                            }\n                          }}\n                        >\n                          <SelectTrigger className=\"transition-all focus:ring-2 focus:ring-primary/20\">\n                            <SelectValue placeholder=\"Select purity level\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"95%\">95%</SelectItem>\n                            <SelectItem value=\"96%\">96%</SelectItem>\n                            <SelectItem value=\"97%\">97%</SelectItem>\n                            <SelectItem value=\"98%\">98%</SelectItem>\n                            <SelectItem value=\"99%\">99%</SelectItem>\n                            <SelectItem value=\"99.5%\">99.5%</SelectItem>\n                            <SelectItem value=\"99.9%\">99.9%</SelectItem>\n                            <SelectItem value=\"99.95%\">99.95%</SelectItem>\n                            <SelectItem value=\"99.99%\">99.99%</SelectItem>\n                            <SelectItem value=\"custom\">Custom value...</SelectItem>\n                          </SelectContent>\n                        </Select>\n                        {(!newProduct.purity || ![\"95%\", \"96%\", \"97%\", \"98%\", \"99%\", \"99.5%\", \"99.9%\", \"99.95%\", \"99.99%\"].includes(newProduct.purity)) && (\n                          <Input\n                            placeholder=\"Enter custom purity (e.g., 99.7%)\"\n                            value={newProduct.purity}\n                            onChange={(e) => setNewProduct(prev => ({ ...prev, purity: e.target.value }))}\n                            className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                          />\n                        )}\n                      </div>\n                    </FormField>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <FormField\n                      label=\"Molecular Formula\"\n                      description=\"Chemical formula representation\"\n                    >\n                      <Input\n                        placeholder=\"e.g., C2H6O\"\n                        value={newProduct.molecularFormula}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, molecularFormula: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20 font-mono\"\n                      />\n                    </FormField>\n\n                    <FormField\n                      label=\"Molecular Weight\"\n                      description=\"Molecular weight in grams per mole\"\n                    >\n                      <div className=\"relative\">\n                        <Input\n                          type=\"number\"\n                          placeholder=\"e.g., 46.07\"\n                          value={newProduct.molecularWeight.replace(' g/mol', '')}\n                          onChange={(e) => {\n                            const value = e.target.value;\n                            setNewProduct(prev => ({\n                              ...prev,\n                              molecularWeight: value ? `${value} g/mol` : ''\n                            }));\n                          }}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20 pr-16\"\n                          step=\"0.01\"\n                          min=\"0\"\n                        />\n                        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                          <span className=\"text-sm text-muted-foreground font-medium\">g/mol</span>\n                        </div>\n                      </div>\n                    </FormField>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <FormField\n                      label=\"Appearance\"\n                      description=\"Physical appearance and form\"\n                    >\n                      <Input\n                        placeholder=\"e.g., White crystalline powder\"\n                        value={newProduct.appearance}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, appearance: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                      />\n                    </FormField>\n\n                    <FormField\n                      label=\"Solubility\"\n                      description=\"Solubility characteristics\"\n                    >\n                      <Input\n                        placeholder=\"e.g., Soluble in water\"\n                        value={newProduct.solubility}\n                        onChange={(e) => setNewProduct(prev => ({ ...prev, solubility: e.target.value }))}\n                        className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                      />\n                    </FormField>\n\n                    <FormField\n                      label=\"pH Value\"\n                      description=\"pH range or specific value\"\n                    >\n                      <div className=\"space-y-2\">\n                        <Select\n                          value={newProduct.phValue}\n                          onValueChange={(value) => {\n                            if (value === \"custom\") {\n                              setNewProduct(prev => ({ ...prev, phValue: \"\" }));\n                            } else {\n                              setNewProduct(prev => ({ ...prev, phValue: value }));\n                            }\n                          }}\n                        >\n                          <SelectTrigger className=\"transition-all focus:ring-2 focus:ring-primary/20\">\n                            <SelectValue placeholder=\"Select pH value\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"1.0\">1.0 (Highly Acidic)</SelectItem>\n                            <SelectItem value=\"2.0\">2.0 (Very Acidic)</SelectItem>\n                            <SelectItem value=\"3.0\">3.0 (Acidic)</SelectItem>\n                            <SelectItem value=\"4.0\">4.0 (Acidic)</SelectItem>\n                            <SelectItem value=\"5.0\">5.0 (Weakly Acidic)</SelectItem>\n                            <SelectItem value=\"6.0\">6.0 (Weakly Acidic)</SelectItem>\n                            <SelectItem value=\"7.0\">7.0 (Neutral)</SelectItem>\n                            <SelectItem value=\"8.0\">8.0 (Weakly Basic)</SelectItem>\n                            <SelectItem value=\"9.0\">9.0 (Basic)</SelectItem>\n                            <SelectItem value=\"10.0\">10.0 (Basic)</SelectItem>\n                            <SelectItem value=\"11.0\">11.0 (Very Basic)</SelectItem>\n                            <SelectItem value=\"12.0\">12.0 (Highly Basic)</SelectItem>\n                            <SelectItem value=\"13.0\">13.0 (Highly Basic)</SelectItem>\n                            <SelectItem value=\"14.0\">14.0 (Highly Basic)</SelectItem>\n                            <SelectItem value=\"1.0-2.0\">1.0-2.0 (Highly Acidic Range)</SelectItem>\n                            <SelectItem value=\"2.0-3.0\">2.0-3.0 (Very Acidic Range)</SelectItem>\n                            <SelectItem value=\"6.5-7.5\">6.5-7.5 (Near Neutral Range)</SelectItem>\n                            <SelectItem value=\"7.0-8.0\">7.0-8.0 (Neutral to Basic Range)</SelectItem>\n                            <SelectItem value=\"8.0-9.0\">8.0-9.0 (Basic Range)</SelectItem>\n                            <SelectItem value=\"custom\">Custom value...</SelectItem>\n                          </SelectContent>\n                        </Select>\n                        {(!newProduct.phValue || ![\"1.0\", \"2.0\", \"3.0\", \"4.0\", \"5.0\", \"6.0\", \"7.0\", \"8.0\", \"9.0\", \"10.0\", \"11.0\", \"12.0\", \"13.0\", \"14.0\", \"1.0-2.0\", \"2.0-3.0\", \"6.5-7.5\", \"7.0-8.0\", \"8.0-9.0\"].includes(newProduct.phValue)) && (\n                          <Input\n                            placeholder=\"Enter custom pH value (e.g., 7.2 or 6.8-7.2)\"\n                            value={newProduct.phValue}\n                            onChange={(e) => setNewProduct(prev => ({ ...prev, phValue: e.target.value }))}\n                            className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                          />\n                        )}\n                      </div>\n                    </FormField>\n                  </div>\n                </FormSection>\n\n                {/* Product Details */}\n                <FormSection\n                  title=\"Product Details\"\n                  description=\"Additional product information and specifications\"\n                >\n                  <FormField\n                    label=\"Packaging\"\n                    description=\"Available packaging options and sizes\"\n                  >\n                    <Input\n                      placeholder=\"e.g., 25kg HDPE bags, 500kg jumbo bags\"\n                      value={newProduct.packaging}\n                      onChange={(e) => setNewProduct(prev => ({ ...prev, packaging: e.target.value }))}\n                      className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                    />\n                  </FormField>\n\n                  <FormField\n                    label=\"Features\"\n                    description=\"Key product features (separate with commas)\"\n                  >\n                    <Textarea\n                      placeholder=\"e.g., High purity, Fast dissolution, Stable quality\"\n                      value={newProduct.features}\n                      onChange={(e) => setNewProduct(prev => ({ ...prev, features: e.target.value }))}\n                      rows={2}\n                      className=\"transition-all focus:ring-2 focus:ring-primary/20 resize-none\"\n                    />\n                  </FormField>\n\n                  <FormField\n                    label=\"Applications\"\n                    description=\"Primary use cases and applications (separate with commas)\"\n                  >\n                    <Textarea\n                      placeholder=\"e.g., Pharmaceutical synthesis, Research, Industrial processes\"\n                      value={newProduct.applications}\n                      onChange={(e) => setNewProduct(prev => ({ ...prev, applications: e.target.value }))}\n                      rows={2}\n                      className=\"transition-all focus:ring-2 focus:ring-primary/20 resize-none\"\n                    />\n                  </FormField>\n                </FormSection>\n\n\n\n                {/* Product Settings */}\n                <FormSection\n                  title=\"Product Settings\"\n                  description=\"Configure product status and visibility options\"\n                >\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <FormField\n                      label=\"Status\"\n                      description=\"Current availability status\"\n                    >\n                      <Select\n                        value={newProduct.status}\n                        onValueChange={(value) => setNewProduct(prev => ({ ...prev, status: value }))}\n                      >\n                        <SelectTrigger className=\"transition-all focus:ring-2 focus:ring-primary/20\">\n                          <SelectValue />\n                        </SelectTrigger>\n                        <SelectContent>\n                          <SelectItem value=\"active\">Active</SelectItem>\n                          <SelectItem value=\"inactive\">Inactive</SelectItem>\n                          <SelectItem value=\"pending_review\">Pending Review</SelectItem>\n                        </SelectContent>\n                      </Select>\n                    </FormField>\n\n                    <FormField\n                      label=\"Featured Product\"\n                      description=\"Highlight this product in featured sections\"\n                    >\n                      <div className=\"flex items-center space-x-3 pt-2\">\n                        <input\n                          type=\"checkbox\"\n                          id=\"featured\"\n                          checked={newProduct.featured}\n                          onChange={(e) => setNewProduct(prev => ({ ...prev, featured: e.target.checked }))}\n                          className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/20 transition-all\"\n                        />\n                        <label htmlFor=\"featured\" className=\"text-sm font-normal\">\n                          Mark as Featured Product\n                        </label>\n                      </div>\n                    </FormField>\n                  </div>\n                </FormSection>\n                </div>\n                )}\n              </div>\n\n              <ModalFooter>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowAddDialog(false)}\n                  disabled={isCreatingProduct}\n                  className=\"transition-all hover:bg-muted\"\n                >\n                  Cancel\n                </Button>\n                <Button\n                  onClick={handleAddProduct}\n                  disabled={!newProduct.title.trim() || isCreatingProduct}\n                  className=\"transition-all\"\n                >\n                  {isCreatingProduct ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Creating Product...\n                    </>\n                  ) : (\n                    <>\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      Create Product\n                    </>\n                  )}\n                </Button>\n              </ModalFooter>\n            </ModalContent>\n          </Modal>\n\n          {/* View Product Details Modal */}\n          <Modal\n            open={showViewDialog}\n            onOpenChange={setShowViewDialog}\n            className=\"w-[95vw] max-w-6xl\"\n          >\n            <ModalContent\n              className=\"w-full max-h-[95vh] overflow-hidden flex flex-col\"\n              showCloseButton={true}\n              onClose={() => setShowViewDialog(false)}\n            >\n              <ModalHeader>\n                <ModalTitle className=\"flex items-center gap-2\">\n                  <Eye className=\"h-5 w-5\" />\n                  Product Details\n                </ModalTitle>\n                <ModalDescription>\n                  View detailed information about this chemical product\n                </ModalDescription>\n              </ModalHeader>\n\n              <div className=\"flex-1 overflow-y-auto px-8 py-6\">\n                {selectedProduct && (\n                  <div className=\"space-y-8\">\n                    {/* Basic Information */}\n                    <div className=\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\">\n                      <div className=\"space-y-1\">\n                        <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n                          Basic Information\n                        </h3>\n                      </div>\n                      <div className=\"space-y-4 pt-2\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                          <div className=\"space-y-2\">\n                            <label className=\"text-sm font-medium text-muted-foreground\">Product Title</label>\n                            <div className=\"text-base font-medium\">{selectedProduct.title}</div>\n                          </div>\n                          {selectedProduct.chemicalName && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">Chemical Name</label>\n                              <div className=\"text-base\">{selectedProduct.chemicalName}</div>\n                            </div>\n                          )}\n                        </div>\n                        {selectedProduct.description && (\n                          <div className=\"space-y-2\">\n                            <label className=\"text-sm font-medium text-muted-foreground\">Description</label>\n                            <div\n                              className=\"prose prose-sm max-w-none text-base leading-relaxed\"\n                              dangerouslySetInnerHTML={{ __html: selectedProduct.description }}\n                            />\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Chemical Properties */}\n                    <div className=\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\">\n                      <div className=\"space-y-1\">\n                        <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n                          Chemical Properties\n                        </h3>\n                      </div>\n                      <div className=\"space-y-4 pt-2\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                          {selectedProduct.casNumber && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">CAS Number</label>\n                              <div className=\"text-base font-mono\">{selectedProduct.casNumber}</div>\n                            </div>\n                          )}\n                          {selectedProduct.molecularFormula && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">Molecular Formula</label>\n                              <div className=\"text-base font-mono\">{selectedProduct.molecularFormula}</div>\n                            </div>\n                          )}\n                          {selectedProduct.molecularWeight && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">Molecular Weight</label>\n                              <div className=\"text-base\">{selectedProduct.molecularWeight}</div>\n                            </div>\n                          )}\n                          {selectedProduct.purity && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">Purity</label>\n                              <div className=\"text-base\">{selectedProduct.purity}</div>\n                            </div>\n                          )}\n                          {selectedProduct.appearance && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">Appearance</label>\n                              <div className=\"text-base\">{selectedProduct.appearance}</div>\n                            </div>\n                          )}\n                          {selectedProduct.solubility && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">Solubility</label>\n                              <div className=\"text-base\">{selectedProduct.solubility}</div>\n                            </div>\n                          )}\n                          {selectedProduct.phValue && (\n                            <div className=\"space-y-2\">\n                              <label className=\"text-sm font-medium text-muted-foreground\">pH Value</label>\n                              <div className=\"text-base\">{selectedProduct.phValue}</div>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Product Images */}\n                    {selectedProduct.images && selectedProduct.images.length > 0 && (\n                      <div className=\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\">\n                        <div className=\"space-y-1\">\n                          <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n                            Product Images\n                          </h3>\n                        </div>\n                        <div className=\"space-y-4 pt-2\">\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                            {selectedProduct.images.map((image: any, index: number) => (\n                              <div key={index} className=\"aspect-square rounded-lg overflow-hidden bg-gray-100 border\">\n                                <img\n                                  src={image.url}\n                                  alt={`Product image ${index + 1}`}\n                                  className=\"w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer\"\n                                  onClick={() => window.open(image.url, '_blank')}\n                                />\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Collections */}\n                    {selectedProduct.collections && selectedProduct.collections.length > 0 && (\n                      <div className=\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\">\n                        <div className=\"space-y-1\">\n                          <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n                            Collections\n                          </h3>\n                        </div>\n                        <div className=\"space-y-4 pt-2\">\n                          <div className=\"flex flex-wrap gap-2\">\n                            {selectedProduct.collections.map((collectionId: string) => {\n                              const collection = activeCollections?.find(c => c.collectionId === collectionId);\n                              return collection ? (\n                                <Badge\n                                  key={collectionId}\n                                  variant=\"secondary\"\n                                  className=\"px-3 py-1 text-sm font-medium bg-primary/10 text-primary border-primary/20\"\n                                >\n                                  {collection.title}\n                                </Badge>\n                              ) : (\n                                <Badge\n                                  key={collectionId}\n                                  variant=\"outline\"\n                                  className=\"px-3 py-1 text-sm text-muted-foreground\"\n                                >\n                                  Unknown Collection\n                                </Badge>\n                              );\n                            })}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Status */}\n                    <div className=\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\">\n                      <div className=\"space-y-1\">\n                        <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n                          Status & Settings\n                        </h3>\n                      </div>\n                      <div className=\"space-y-4 pt-2\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                          <div className=\"space-y-2\">\n                            <label className=\"text-sm font-medium text-muted-foreground\">Status</label>\n                            <div>{getStatusBadge(selectedProduct.status)}</div>\n                          </div>\n                          <div className=\"space-y-2\">\n                            <label className=\"text-sm font-medium text-muted-foreground\">Featured</label>\n                            <div>\n                              {selectedProduct.featured ? (\n                                <Badge variant=\"outline\" className=\"text-yellow-600 border-yellow-600\">\n                                  <Star className=\"h-3 w-3 mr-1\" />\n                                  Featured\n                                </Badge>\n                              ) : (\n                                <span className=\"text-muted-foreground\">Not featured</span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <ModalFooter>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowViewDialog(false)}\n                >\n                  Close\n                </Button>\n                <Button\n                  onClick={() => {\n                    setShowViewDialog(false);\n                    handleEditProduct(selectedProduct);\n                  }}\n                >\n                  <Edit className=\"h-4 w-4 mr-2\" />\n                  Edit Product\n                </Button>\n              </ModalFooter>\n            </ModalContent>\n          </Modal>\n\n          {/* Edit Product Modal */}\n          <Modal\n            open={showEditDialog}\n            onOpenChange={(open) => {\n              setShowEditDialog(open);\n              if (!open) {\n                setSelectedProduct(null);\n              }\n            }}\n            className=\"w-[95vw] max-w-7xl\"\n          >\n            <ModalContent\n              className=\"w-full max-h-[95vh] overflow-hidden flex flex-col\"\n              showCloseButton={true}\n              onClose={() => {\n                setShowEditDialog(false);\n                setSelectedProduct(null);\n              }}\n            >\n              <ModalHeader>\n                <ModalTitle className=\"flex items-center gap-2\">\n                  {isUpdatingProduct && <Loader2 className=\"h-4 w-4 animate-spin\" />}\n                  Edit Product\n                </ModalTitle>\n                <ModalDescription>\n                  Update the chemical product information and properties\n                </ModalDescription>\n              </ModalHeader>\n\n              <div className=\"flex-1 overflow-y-auto px-8 py-6\">\n                {!selectedProduct ? (\n                  // Skeleton loading for edit modal\n                  <div className=\"space-y-10\">\n                    {/* Basic Information Skeleton */}\n                    <div className=\"space-y-6\">\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-6 w-48\" />\n                        <Skeleton className=\"h-4 w-96\" />\n                      </div>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        <div className=\"space-y-2\">\n                          <Skeleton className=\"h-4 w-24\" />\n                          <Skeleton className=\"h-10 w-full\" />\n                        </div>\n                        <div className=\"space-y-2\">\n                          <Skeleton className=\"h-4 w-24\" />\n                          <Skeleton className=\"h-10 w-full\" />\n                        </div>\n                      </div>\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-4 w-24\" />\n                        <Skeleton className=\"h-32 w-full\" />\n                      </div>\n                    </div>\n\n                    {/* Chemical Properties Skeleton */}\n                    <div className=\"space-y-6\">\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-6 w-48\" />\n                        <Skeleton className=\"h-4 w-96\" />\n                      </div>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                        {Array.from({ length: 6 }).map((_, index) => (\n                          <div key={index} className=\"space-y-2\">\n                            <Skeleton className=\"h-4 w-24\" />\n                            <Skeleton className=\"h-10 w-full\" />\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* Collections Skeleton */}\n                    <div className=\"space-y-6\">\n                      <div className=\"space-y-2\">\n                        <Skeleton className=\"h-6 w-32\" />\n                        <Skeleton className=\"h-4 w-64\" />\n                      </div>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                        {Array.from({ length: 4 }).map((_, index) => (\n                          <div key={index} className=\"flex items-center space-x-3 p-3 border border-border/50 rounded-lg\">\n                            <Skeleton className=\"h-4 w-4 rounded\" />\n                            <div className=\"flex-1 space-y-2\">\n                              <Skeleton className=\"h-4 w-3/4\" />\n                              <Skeleton className=\"h-3 w-1/2\" />\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-10\">\n                    {/* Basic Information */}\n                    <FormSection\n                      title=\"Basic Information\"\n                      description=\"Essential product details and identification\"\n                    >\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <FormField\n                        label=\"Product Title\"\n                        required\n                        description=\"The main name of your chemical product\"\n                      >\n                        <Input\n                          placeholder=\"Enter product title\"\n                          value={editProduct.title}\n                          onChange={(e) => setEditProduct(prev => ({ ...prev, title: e.target.value }))}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                        />\n                      </FormField>\n\n                      <FormField\n                        label=\"Chemical Name\"\n                        description=\"IUPAC or common chemical name\"\n                      >\n                        <Input\n                          placeholder=\"Enter chemical name\"\n                          value={editProduct.chemicalName}\n                          onChange={(e) => setEditProduct(prev => ({ ...prev, chemicalName: e.target.value }))}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                        />\n                      </FormField>\n                    </div>\n\n                    <FormField\n                      label=\"Description\"\n                      description=\"Detailed description of the product and its properties\"\n                    >\n                      <RichTextEditor\n                        content={editProduct.description}\n                        onChange={(content) => setEditProduct(prev => ({ ...prev, description: content }))}\n                        placeholder=\"Enter a detailed product description with formatting...\"\n                        className=\"min-h-[200px]\"\n                      />\n                    </FormField>\n                  </FormSection>\n\n                  {/* Chemical Properties */}\n                  <FormSection\n                    title=\"Chemical Properties\"\n                    description=\"Specific chemical characteristics and identifiers\"\n                  >\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                      <FormField\n                        label=\"CAS Number\"\n                        description=\"Chemical Abstracts Service registry number\"\n                      >\n                        <Input\n                          placeholder=\"e.g., 64-17-5\"\n                          value={editProduct.casNumber}\n                          onChange={(e) => setEditProduct(prev => ({ ...prev, casNumber: e.target.value }))}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                        />\n                      </FormField>\n\n                      <FormField\n                        label=\"Molecular Formula\"\n                        description=\"Chemical formula representation\"\n                      >\n                        <Input\n                          placeholder=\"e.g., C2H6O\"\n                          value={editProduct.molecularFormula}\n                          onChange={(e) => setEditProduct(prev => ({ ...prev, molecularFormula: e.target.value }))}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                        />\n                      </FormField>\n\n                      <FormField\n                        label=\"Molecular Weight\"\n                        description=\"Molecular weight in g/mol\"\n                      >\n                        <div className=\"relative\">\n                          <Input\n                            type=\"number\"\n                            placeholder=\"e.g., 46.07\"\n                            value={editProduct.molecularWeight.replace(' g/mol', '')}\n                            onChange={(e) => {\n                              const value = e.target.value;\n                              setEditProduct(prev => ({\n                                ...prev,\n                                molecularWeight: value ? `${value} g/mol` : ''\n                              }));\n                            }}\n                            className=\"transition-all focus:ring-2 focus:ring-primary/20 pr-16\"\n                            step=\"0.01\"\n                            min=\"0\"\n                          />\n                          <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                            <span className=\"text-sm text-muted-foreground font-medium\">g/mol</span>\n                          </div>\n                        </div>\n                      </FormField>\n\n                      <FormField\n                        label=\"Purity\"\n                        description=\"Chemical purity percentage\"\n                      >\n                        <div className=\"space-y-2\">\n                          <Select\n                            value={editProduct.purity}\n                            onValueChange={(value) => {\n                              if (value === \"custom\") {\n                                setEditProduct(prev => ({ ...prev, purity: \"\" }));\n                              } else {\n                                setEditProduct(prev => ({ ...prev, purity: value }));\n                              }\n                            }}\n                          >\n                            <SelectTrigger className=\"transition-all focus:ring-2 focus:ring-primary/20\">\n                              <SelectValue placeholder=\"Select purity level\" />\n                            </SelectTrigger>\n                            <SelectContent>\n                              <SelectItem value=\"95%\">95%</SelectItem>\n                              <SelectItem value=\"96%\">96%</SelectItem>\n                              <SelectItem value=\"97%\">97%</SelectItem>\n                              <SelectItem value=\"98%\">98%</SelectItem>\n                              <SelectItem value=\"99%\">99%</SelectItem>\n                              <SelectItem value=\"99.5%\">99.5%</SelectItem>\n                              <SelectItem value=\"99.9%\">99.9%</SelectItem>\n                              <SelectItem value=\"99.95%\">99.95%</SelectItem>\n                              <SelectItem value=\"99.99%\">99.99%</SelectItem>\n                              <SelectItem value=\"custom\">Custom value...</SelectItem>\n                            </SelectContent>\n                          </Select>\n                          {(!editProduct.purity || ![\"95%\", \"96%\", \"97%\", \"98%\", \"99%\", \"99.5%\", \"99.9%\", \"99.95%\", \"99.99%\"].includes(editProduct.purity)) && (\n                            <Input\n                              placeholder=\"Enter custom purity (e.g., 99.7%)\"\n                              value={editProduct.purity}\n                              onChange={(e) => setEditProduct(prev => ({ ...prev, purity: e.target.value }))}\n                              className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                            />\n                          )}\n                        </div>\n                      </FormField>\n\n                      <FormField\n                        label=\"Appearance\"\n                        description=\"Physical appearance description\"\n                      >\n                        <Input\n                          placeholder=\"e.g., Clear liquid\"\n                          value={editProduct.appearance}\n                          onChange={(e) => setEditProduct(prev => ({ ...prev, appearance: e.target.value }))}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                        />\n                      </FormField>\n\n                      <FormField\n                        label=\"Solubility\"\n                        description=\"Solubility characteristics\"\n                      >\n                        <Input\n                          placeholder=\"e.g., Miscible with water\"\n                          value={editProduct.solubility}\n                          onChange={(e) => setEditProduct(prev => ({ ...prev, solubility: e.target.value }))}\n                          className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                        />\n                      </FormField>\n\n                      <FormField\n                        label=\"pH Value\"\n                        description=\"pH range or specific value\"\n                      >\n                        <div className=\"space-y-2\">\n                          <Select\n                            value={editProduct.phValue}\n                            onValueChange={(value) => {\n                              if (value === \"custom\") {\n                                setEditProduct(prev => ({ ...prev, phValue: \"\" }));\n                              } else {\n                                setEditProduct(prev => ({ ...prev, phValue: value }));\n                              }\n                            }}\n                          >\n                            <SelectTrigger className=\"transition-all focus:ring-2 focus:ring-primary/20\">\n                              <SelectValue placeholder=\"Select pH value\" />\n                            </SelectTrigger>\n                            <SelectContent>\n                              <SelectItem value=\"1.0\">1.0 (Highly Acidic)</SelectItem>\n                              <SelectItem value=\"2.0\">2.0 (Very Acidic)</SelectItem>\n                              <SelectItem value=\"3.0\">3.0 (Acidic)</SelectItem>\n                              <SelectItem value=\"4.0\">4.0 (Acidic)</SelectItem>\n                              <SelectItem value=\"5.0\">5.0 (Weakly Acidic)</SelectItem>\n                              <SelectItem value=\"6.0\">6.0 (Weakly Acidic)</SelectItem>\n                              <SelectItem value=\"7.0\">7.0 (Neutral)</SelectItem>\n                              <SelectItem value=\"8.0\">8.0 (Weakly Basic)</SelectItem>\n                              <SelectItem value=\"9.0\">9.0 (Basic)</SelectItem>\n                              <SelectItem value=\"10.0\">10.0 (Basic)</SelectItem>\n                              <SelectItem value=\"11.0\">11.0 (Very Basic)</SelectItem>\n                              <SelectItem value=\"12.0\">12.0 (Highly Basic)</SelectItem>\n                              <SelectItem value=\"13.0\">13.0 (Highly Basic)</SelectItem>\n                              <SelectItem value=\"14.0\">14.0 (Highly Basic)</SelectItem>\n                              <SelectItem value=\"1.0-2.0\">1.0-2.0 (Highly Acidic Range)</SelectItem>\n                              <SelectItem value=\"2.0-3.0\">2.0-3.0 (Very Acidic Range)</SelectItem>\n                              <SelectItem value=\"6.5-7.5\">6.5-7.5 (Near Neutral Range)</SelectItem>\n                              <SelectItem value=\"7.0-8.0\">7.0-8.0 (Neutral to Basic Range)</SelectItem>\n                              <SelectItem value=\"8.0-9.0\">8.0-9.0 (Basic Range)</SelectItem>\n                              <SelectItem value=\"custom\">Custom value...</SelectItem>\n                            </SelectContent>\n                          </Select>\n                          {(!editProduct.phValue || ![\"1.0\", \"2.0\", \"3.0\", \"4.0\", \"5.0\", \"6.0\", \"7.0\", \"8.0\", \"9.0\", \"10.0\", \"11.0\", \"12.0\", \"13.0\", \"14.0\", \"1.0-2.0\", \"2.0-3.0\", \"6.5-7.5\", \"7.0-8.0\", \"8.0-9.0\"].includes(editProduct.phValue)) && (\n                            <Input\n                              placeholder=\"Enter custom pH value (e.g., 7.2 or 6.8-7.2)\"\n                              value={editProduct.phValue}\n                              onChange={(e) => setEditProduct(prev => ({ ...prev, phValue: e.target.value }))}\n                              className=\"transition-all focus:ring-2 focus:ring-primary/20\"\n                            />\n                          )}\n                        </div>\n                      </FormField>\n                    </div>\n                  </FormSection>\n\n                  {/* Collections */}\n                  <FormSection\n                    title=\"Collections\"\n                    description=\"Assign this product to collections for better organization\"\n                  >\n                    <FormField\n                      label=\"Product Collections\"\n                      description=\"Select which collections this product belongs to\"\n                    >\n                      <div className=\"space-y-3\">\n                        {activeCollections === undefined ? (\n                          // Skeleton loading for collections\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                            {Array.from({ length: 4 }).map((_, index) => (\n                              <div key={index} className=\"flex items-center space-x-3 p-3 border border-border/50 rounded-lg bg-muted/20\">\n                                <Skeleton className=\"h-4 w-4 rounded\" />\n                                <div className=\"flex-1 space-y-2\">\n                                  <Skeleton className=\"h-4 w-3/4\" />\n                                  <Skeleton className=\"h-3 w-1/2\" />\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        ) : activeCollections && activeCollections.length > 0 ? (\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                            {activeCollections.map((collection) => (\n                              <div\n                                key={collection._id}\n                                className=\"flex items-center space-x-3 p-3 border border-border/50 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors\"\n                              >\n                                <input\n                                  type=\"checkbox\"\n                                  id={`edit-collection-${collection._id}`}\n                                  checked={editProduct.collections.includes(collection.collectionId)}\n                                  onChange={(e) => {\n                                    const collectionId = collection.collectionId;\n                                    setEditProduct(prev => ({\n                                      ...prev,\n                                      collections: e.target.checked\n                                        ? [...prev.collections, collectionId]\n                                        : prev.collections.filter(id => id !== collectionId)\n                                    }));\n                                  }}\n                                  className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/20\"\n                                />\n                                <label\n                                  htmlFor={`edit-collection-${collection._id}`}\n                                  className=\"flex-1 cursor-pointer\"\n                                >\n                                  <div className=\"font-medium text-sm\">{collection.title}</div>\n                                  {collection.description && (\n                                    <div className=\"text-xs text-muted-foreground\">\n                                      {collection.description}\n                                    </div>\n                                  )}\n                                </label>\n                              </div>\n                            ))}\n                          </div>\n                        ) : (\n                          <div className=\"text-center py-8 text-muted-foreground\">\n                            <Package className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                            <p className=\"text-sm\">No collections available</p>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={initializeCollections}\n                              className=\"mt-2\"\n                            >\n                              Create Sample Collections\n                            </Button>\n                          </div>\n                        )}\n                      </div>\n                    </FormField>\n                  </FormSection>\n\n                  {/* Status and Settings */}\n                  <FormSection\n                    title=\"Status & Settings\"\n                    description=\"Product status and visibility settings\"\n                  >\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <FormField\n                        label=\"Status\"\n                        description=\"Product availability status\"\n                      >\n                        <Select\n                          value={editProduct.status}\n                          onValueChange={(value) => setEditProduct(prev => ({ ...prev, status: value }))}\n                        >\n                          <SelectTrigger className=\"transition-all focus:ring-2 focus:ring-primary/20\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"active\">Active</SelectItem>\n                            <SelectItem value=\"inactive\">Inactive</SelectItem>\n                            <SelectItem value=\"discontinued\">Discontinued</SelectItem>\n                            <SelectItem value=\"pending_review\">Pending Review</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      </FormField>\n\n                      <FormField\n                        label=\"Featured\"\n                        description=\"Mark as featured product\"\n                      >\n                        <div className=\"flex items-center space-x-3 p-4 border border-border/50 rounded-lg bg-muted/20\">\n                          <input\n                            type=\"checkbox\"\n                            checked={editProduct.featured}\n                            onChange={(e) => setEditProduct(prev => ({ ...prev, featured: e.target.checked }))}\n                            className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/20\"\n                          />\n                          <div className=\"flex-1\">\n                            <div className=\"font-medium text-sm\">Featured Product</div>\n                            <div className=\"text-xs text-muted-foreground\">\n                              Show this product prominently\n                            </div>\n                          </div>\n                        </div>\n                      </FormField>\n                    </div>\n                  </FormSection>\n                </div>\n                )}\n              </div>\n\n              <ModalFooter>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowEditDialog(false);\n                    setSelectedProduct(null);\n                  }}\n                  disabled={isUpdatingProduct}\n                  className=\"transition-all hover:bg-muted\"\n                >\n                  Cancel\n                </Button>\n                <Button\n                  onClick={handleUpdateProduct}\n                  disabled={!editProduct.title.trim() || isUpdatingProduct}\n                  className=\"transition-all\"\n                >\n                  {isUpdatingProduct ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Updating Product...\n                    </>\n                  ) : (\n                    <>\n                      <Edit className=\"h-4 w-4 mr-2\" />\n                      Update Product\n                    </>\n                  )}\n                </Button>\n              </ModalFooter>\n            </ModalContent>\n          </Modal>\n\n          {/* Delete Product Confirmation Dialog */}\n          <Modal\n            open={showDeleteDialog}\n            onOpenChange={setShowDeleteDialog}\n            className=\"w-[95vw] max-w-md\"\n          >\n            <ModalContent\n              className=\"w-full\"\n              showCloseButton={true}\n              onClose={() => {\n                setShowDeleteDialog(false);\n                setProductToDelete(null);\n              }}\n            >\n              <ModalHeader>\n                <ModalTitle className=\"flex items-center gap-2 text-red-600\">\n                  <Trash2 className=\"h-5 w-5\" />\n                  Delete Product\n                </ModalTitle>\n                <ModalDescription>\n                  This action cannot be undone. This will permanently delete the product and remove it from all collections.\n                </ModalDescription>\n              </ModalHeader>\n\n              <div className=\"px-6 py-4\">\n                {productToDelete && (\n                  <div className=\"space-y-4\">\n                    <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                      <div className=\"flex items-start gap-3\">\n                        <div className=\"flex-shrink-0\">\n                          {productToDelete.images && productToDelete.images.length > 0 ? (\n                            <img\n                              src={productToDelete.images[0].url}\n                              alt={productToDelete.title}\n                              className=\"w-12 h-12 rounded object-cover\"\n                            />\n                          ) : (\n                            <div className=\"w-12 h-12 bg-gray-200 rounded flex items-center justify-center\">\n                              <Package className=\"h-6 w-6 text-gray-400\" />\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <h4 className=\"font-medium text-gray-900 truncate\">\n                            {productToDelete.title}\n                          </h4>\n                          {productToDelete.casNumber && (\n                            <p className=\"text-sm text-gray-600\">\n                              CAS: {productToDelete.casNumber}\n                            </p>\n                          )}\n                          {productToDelete.collections && productToDelete.collections.length > 0 && (\n                            <p className=\"text-sm text-gray-600\">\n                              Collections: {productToDelete.collections.length}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"font-medium mb-2\">This will:</p>\n                      <ul className=\"list-disc list-inside space-y-1 text-sm\">\n                        <li>Permanently delete the product from the database</li>\n                        <li>Remove the product from all associated collections</li>\n                        <li>Update collection product counts</li>\n                        <li>Create an activity log entry</li>\n                      </ul>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <ModalFooter>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowDeleteDialog(false);\n                    setProductToDelete(null);\n                  }}\n                  disabled={isDeletingProduct}\n                  className=\"transition-all hover:bg-muted\"\n                >\n                  Cancel\n                </Button>\n                <Button\n                  onClick={confirmDeleteProduct}\n                  disabled={isDeletingProduct}\n                  className=\"bg-red-600 hover:bg-red-700 text-white transition-all\"\n                >\n                  {isDeletingProduct ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Deleting...\n                    </>\n                  ) : (\n                    <>\n                      <Trash2 className=\"h-4 w-4 mr-2\" />\n                      Delete Product\n                    </>\n                  )}\n                </Button>\n              </ModalFooter>\n            </ModalContent>\n          </Modal>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAQA;AAOA;AAQA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AAAA;AACA;AACA;AACA;AAtEA;;;;;;;;;;;;;;;;;;;;;;AAwEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,WAAW;IAEjB,yBAAyB;IACzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,aAAa;QACb,WAAW;QACX,kBAAkB;QAClB,iBAAiB;QACjB,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,cAAc;QACd,WAAW;QACX,UAAU;QACV,cAAc;QACd,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,aAAa,EAAE;IACjB;IAEA,0BAA0B;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,OAAO;QACP,aAAa;QACb,WAAW;QACX,kBAAkB;QAClB,iBAAiB;QACjB,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,cAAc;QACd,WAAW;QACX,UAAU;QACV,cAAc;QACd,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,aAAa,EAAE;IACjB;IAEA,sBAAsB;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAExB,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC,MAAY,WAAmB,GAAG,EAAE,UAAkB,GAAG;QAC9E,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,IAAI,MAAM,GAAG;gBACX,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBACxB,IAAI,QAAQ,UAAU;oBACpB,SAAS,AAAC,SAAS,WAAY;oBAC/B,QAAQ;gBACV;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,KAAK,UAAU,KAAK,GAAG,GAAG,OAAO;gBACjC,MAAM,oBAAoB,OAAO,SAAS,CAAC,cAAc;gBAEzD,6DAA6D;gBAC7D,IAAI,kBAAkB,MAAM,GAAG,MAAM,OAAO,IAAE,GAAG;oBAC/C,OAAO,IAAI,MAAM;gBACnB,OAAO;oBACL,QAAQ;gBACV;YACF;YAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;QAChC;IACF;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,OAAO;QAC9B,mBAAmB;QACnB,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO;gBAClD,qBAAqB;gBACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,MAAM,IAAI,MAAM,GAAG,KAAK,IAAI,CAAC,qBAAqB,CAAC;gBACrD;gBAEA,sDAAsD;gBACtD,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,MAAM,IAAI,MAAM,GAAG,KAAK,IAAI,CAAC,uBAAuB,CAAC;gBACvD;gBAEA,mCAAmC;gBACnC,IAAI;oBACF,OAAO,MAAM,cAAc;gBAC7B,EAAE,OAAO,kBAAkB;oBACzB,MAAM,IAAI,MAAM,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,4BAA4B,QAAQ,iBAAiB,OAAO,GAAG,sBAAsB;gBACxH;YACF;YAEA,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC;YAEpC,sCAAsC;YACtC,MAAM,kBAAkB,KAAK,SAAS,CAAC,YAAY,MAAM;YACzD,MAAM,cAAc,KAAK,SAAS,CAAC,WAAW,MAAM;YACpD,MAAM,YAAY,kBAAkB;YAEpC,iEAAiE;YACjE,IAAI,YAAY,QAAQ;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;2BAAK;qBAAU;gBACxC,CAAC;YAED,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,MAAM,CAAC,8CAA8C,CAAC;QACnF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,IAAI,IAAI,IAAI;QAEjB,uBAAuB;QACvB,IAAI;YACF,IAAI,IAAI;YACR,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;wBAAE,IAAI,IAAI;qBAAG;gBACtC,CAAC;YACD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB;QACnB;IACF;IAEA,UAAU;IACV,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,WAAW,EAAE;QAClD,QAAQ,cAAc;QACtB,QAAQ,iBAAiB,QAAQ,YAAY;QAC7C,OAAO;QACP,QAAQ,cAAc;IACxB;IAEA,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,eAAe;IAC1D,MAAM,oBAAoB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,WAAW,CAAC,oBAAoB;IAEvE,YAAY;IACZ,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,qBAAqB;IACrE,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,mBAAmB;IACjE,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa;IAC5D,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa;IAC5D,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa;IAC5D,MAAM,uBAAuB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,MAAM,CAAC,oBAAoB;IACxE,MAAM,0BAA0B,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,WAAW,CAAC,uBAAuB;IAEnF,uCAAuC;IACvC,MAAM,wBAAwB;QAC5B,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAChE,MAAM,wBAAwB;gBAAE;YAAQ;YACxC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,yCAAyC;YACzC,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,eAAe;gBACnB,WAAW;gBACX,SAAS;YACX;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,yCAAyC;YACzC,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,aAAa;gBACjB,WAAW;gBACX,QAAQ;gBACR,SAAS;YACX;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,4CAA4C;IAC5C,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,qCAAqC;IACrC,MAAM,uBAAuB;QAC3B,IAAI,CAAC,SAAS,CAAC,iBAAiB;QAEhC,qBAAqB;QACrB,IAAI;YACF,yCAAyC;YACzC,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,cAAc;gBAClB,WAAW,gBAAgB,GAAG;gBAC9B,SAAS;YACX;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,oBAAoB;YACpB,mBAAmB;YAEnB,6DAA6D;YAC7D,IAAI,iBAAiB,QAAQ,gBAAgB,GAAG,EAAE;gBAChD,kBAAkB;gBAClB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACrD,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,oCAAoC;IACpC,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,oCAAoC;IACpC,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QAEnB,uCAAuC;QACvC,eAAe;YACb,OAAO,QAAQ,KAAK,IAAI;YACxB,aAAa,QAAQ,WAAW,IAAI;YACpC,WAAW,QAAQ,SAAS,IAAI;YAChC,kBAAkB,QAAQ,gBAAgB,IAAI;YAC9C,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,QAAQ,QAAQ,MAAM,IAAI;YAC1B,WAAW,QAAQ,SAAS,IAAI;YAChC,YAAY,QAAQ,UAAU,IAAI;YAClC,YAAY,QAAQ,UAAU,IAAI;YAClC,SAAS,QAAQ,OAAO,IAAI;YAC5B,cAAc,QAAQ,YAAY,IAAI;YACtC,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ;YAC3D,cAAc,QAAQ,YAAY,GAAG,QAAQ,YAAY,CAAC,IAAI,CAAC,QAAQ;YACvE,QAAQ,QAAQ,MAAM,IAAI;YAC1B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,MAAa,IAAI,GAAG,IAAI,EAAE;YACvE,aAAa,QAAQ,WAAW,IAAI,EAAE;QACxC;QAEA,kBAAkB;IACpB;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,CAAC,iBAAiB;QAEhC,qBAAqB;QACrB,IAAI;YACF,sBAAsB;YACtB,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,IAAI;gBAC7B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,yEAAyE;YACzE,MAAM,kBAAkB,YAAY,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI;YAC5E,IAAI,CAAC,iBAAiB;gBACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,oCAAoC;YACpC,MAAM,gBAAgB,YAAY,MAAM,CAAC,MAAM,CAAC,CAAA;gBAC9C,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO;gBACxB,gDAAgD;gBAChD,IAAI,IAAI,UAAU,CAAC,UAAU,OAAO;gBACpC,IAAI;oBACF,IAAI,IAAI;oBACR,OAAO;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,kCAAkC;YAClC,MAAM,cAAc;gBAClB,WAAW,gBAAgB,SAAS,IAAI,gBAAgB,GAAG;gBAC3D,OAAO,YAAY,KAAK,CAAC,IAAI;gBAC7B,aAAa,YAAY,WAAW;gBACpC,iBAAiB,YAAY,WAAW;gBACxC,WAAW,YAAY,SAAS,CAAC,IAAI,MAAM;gBAC3C,kBAAkB,YAAY,gBAAgB,CAAC,IAAI,MAAM;gBACzD,iBAAiB,YAAY,eAAe,CAAC,IAAI,MAAM;gBACvD,QAAQ,YAAY,MAAM,IAAI;gBAC9B,WAAW,YAAY,SAAS,CAAC,IAAI,MAAM;gBAC3C,YAAY,YAAY,UAAU,CAAC,IAAI,MAAM;gBAC7C,YAAY,YAAY,UAAU,CAAC,IAAI,MAAM;gBAC7C,SAAS,YAAY,OAAO,IAAI;gBAChC,cAAc,YAAY,YAAY,CAAC,IAAI,MAAM;gBACjD,WAAW,YAAY,SAAS,CAAC,IAAI,MAAM;gBAC3C,UAAU,YAAY,QAAQ,CAAC,IAAI,KAAK,YAAY,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM;gBAC7F,cAAc,YAAY,YAAY,CAAC,IAAI,KAAK,YAAY,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM;gBACzG,aAAa,YAAY,WAAW;gBACpC,QAAQ,YAAY,MAAM,CACvB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,IACtB,GAAG,CAAC,CAAA,MAAO,CAAC;wBAAE,KAAK,IAAI,IAAI;oBAAG,CAAC;gBAClC,QAAQ,YAAY,MAAM;gBAC1B,UAAU,YAAY,QAAQ;gBAC9B,MAAM,EAAE;gBACR,6DAA6D;gBAC7D,YAAY,gBAAgB,UAAU,IAAI;oBACxC,iBAAiB;wBAAE,QAAQ;wBAAK,cAAc;oBAAM;oBACpD,iBAAiB;wBAAE,QAAQ;wBAAK,cAAc;oBAAM;gBACtD;YACF;YAEA,0CAA0C;YAC1C,MAAM,WAAW,KAAK,SAAS,CAAC,aAAa,MAAM;YACnD,IAAI,WAAW,QAAQ;gBACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,yCAAyC;YACzC,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,cAAc;gBAClB,GAAG,WAAW;gBACd,SAAS;YACX;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,kBAAkB;YAClB,mBAAmB;QAErB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO;QAEZ,qBAAqB;QACrB,IAAI;YACF,sBAAsB;YACtB,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,IAAI;gBAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,yEAAyE;YACzE,MAAM,kBAAkB,WAAW,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI;YAC3E,IAAI,CAAC,iBAAiB;gBACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,oCAAoC;YACpC,MAAM,gBAAgB,WAAW,MAAM,CAAC,MAAM,CAAC,CAAA;gBAC7C,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO;gBACxB,gDAAgD;gBAChD,IAAI,IAAI,UAAU,CAAC,UAAU,OAAO;gBACpC,IAAI;oBACF,IAAI,IAAI;oBACR,OAAO;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,0CAA0C;YAC1C,MAAM,cAAc;gBAClB,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBAC/B,OAAO,WAAW,KAAK,CAAC,IAAI;gBAC5B,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,SAAS,CAAC,IAAI,MAAM;gBAC1C,kBAAkB,WAAW,gBAAgB,CAAC,IAAI,MAAM;gBACxD,iBAAiB,WAAW,eAAe,CAAC,IAAI,MAAM;gBACtD,QAAQ,WAAW,MAAM,IAAI;gBAC7B,WAAW,WAAW,SAAS,CAAC,IAAI,MAAM;gBAC1C,YAAY,WAAW,UAAU,CAAC,IAAI,MAAM;gBAC5C,YAAY,WAAW,UAAU,CAAC,IAAI,MAAM;gBAC5C,SAAS,WAAW,OAAO,IAAI;gBAC/B,cAAc,WAAW,YAAY,CAAC,IAAI,MAAM;gBAChD,WAAW,WAAW,SAAS,CAAC,IAAI,MAAM;gBAC1C,UAAU,WAAW,QAAQ,CAAC,IAAI,KAAK,WAAW,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM;gBAC3F,cAAc,WAAW,YAAY,CAAC,IAAI,KAAK,WAAW,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM;gBACvG,aAAa,WAAW,WAAW;gBACnC,QAAQ,WAAW,MAAM,CACtB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,IACtB,GAAG,CAAC,CAAA,MAAO,CAAC;wBAAE,KAAK,IAAI,IAAI;oBAAG,CAAC;gBAClC,QAAQ,WAAW,MAAM;gBACzB,UAAU,WAAW,QAAQ;YAC/B;YAEA,MAAM,WAAW,KAAK,SAAS,CAAC,aAAa,MAAM;YACnD,IAAI,WAAW,QAAQ;gBACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,qBAAqB;gBACrB;YACF;YAEA,yCAAyC;YACzC,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,cAAc;gBAClB,GAAG,WAAW;gBACd,SAAS;YACX;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,iBAAiB;YAEjB,+BAA+B;YAC/B,cAAc;gBACZ,OAAO;gBACP,aAAa;gBACb,WAAW;gBACX,kBAAkB;gBAClB,iBAAiB;gBACjB,QAAQ;gBACR,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,SAAS;gBACT,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV,cAAc;gBACd,QAAQ;gBACR,UAAU;gBACV,QAAQ,EAAE;gBACV,aAAa,EAAE;YACjB;YAEA,sBAAsB;YACtB,mBAAmB;YACnB,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,oDAAoD;YACpD,MAAM,gBAAgB;YACtB,cAAc;YACd,WAAW;gBACT,cAAc;gBACd,gBAAgB;gBAChB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,GAAG;QACL,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,eAAe;QACf,IAAI;YACF,mBAAmB;YACnB,MAAM,aAAa;gBACjB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,UAAW;oBACtC,QAAQ,SAAS,IAAI,QAAQ,GAAG;oBAChC,QAAQ,KAAK;oBACb,QAAQ,WAAW,IAAI;oBACvB,QAAQ,SAAS,IAAI;oBACrB,QAAQ,gBAAgB,IAAI;oBAC5B,QAAQ,eAAe,IAAI;oBAC3B,QAAQ,MAAM,IAAI;oBAClB,QAAQ,OAAO,IAAI;oBACnB,QAAQ,UAAU,IAAI;oBACtB,QAAQ,UAAU,IAAI;oBACtB,QAAQ,MAAM;oBACd,QAAQ,QAAQ,GAAG,QAAQ;oBAC3B,WAAW,QAAQ,SAAS;oBAC5B,WAAW,QAAQ,SAAS;iBAC7B;YAED,qBAAqB;YACrB,MAAM,aAAa;gBACjB,WAAW,IAAI,CAAC;mBACb,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;aACzD,CAAC,IAAI,CAAC;YAEP,2BAA2B;YAC3B,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAA0B;YACtE,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,KAAK,YAAY,CAAC,QAAQ;YAC1B,KAAK,YAAY,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC7F,KAAK,KAAK,CAAC,UAAU,GAAG;YACxB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAe;;;;;;YAC3D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAoC;;;;;;YAChF;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,SAAS;YACrD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,8OAAC,gJAAA,CAAA,iBAAc;QAAC,oBAAmB;kBACjC,cAAA,8OAAC,mJAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,eAAe,CAAC,YAAY,SAAS,MAAM,KAAK;;0DAE1D,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,aAAa,EAAE,cAAc,iBAAiB,IAAI;;;;;;4CACvE,cAAc,iBAAiB;;;;;;;kDAElC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,aAAa,EAAE,eAAe,iBAAiB,IAAI;;;;;;4CACzE,eAAe,kBAAkB;;;;;;;kDAEpC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAS,IAAM,iBAAiB;;0DAChD,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,cAAc,SAAS;;;;;;;;;;;;;;;;;0CAGhE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAqC,cAAc,UAAU;;;;;;;;;;;;;;;;;0CAGhF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAoC,cAAc,YAAY;;;;;;;;;;;;;;;;;0CAGjF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAoC,cAAc,YAAY;;;;;;;;;;;;;;;;;0CAGjF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsC,cAAc,eAAe;;;;;;;;;;;;;;;;;;;;;;;kCAMxF,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAW,CAAC,iDAAiD,EACnE,YAAY,SAAS,MAAM,GAAG,IAAI,0BAA0B,4BAC5D;;;;;;kEACF,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAW,CAAC,MAAM,EAChB,YAAY,SAAS,MAAM,GAAG,IAAI,KAAK,iCACvC;wDACF,UAAU,CAAC,YAAY,SAAS,MAAM,KAAK;;;;;;;;;;;;0DAG/C,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,eAAe;gDACf,UAAU,CAAC,YAAY,SAAS,MAAM,KAAK;;kEAE3C,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAW,CAAC,UAAU,EACnC,YAAY,SAAS,MAAM,GAAG,IAAI,KAAK,iCACvC;kEACA,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;0EACjC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;;;;;;;;;;;;;;;;;;;oCAKxC,CAAC,WACA,yBAAyB;kDACzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8DACJ,8OAAC,iIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0EACP,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAa;;;;;;;;;;;;;;;;;8DAGtC,8OAAC,iIAAA,CAAA,YAAS;8DACP,MAAM,IAAI,CAAC;wDAAE,QAAQ;oDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,iIAAA,CAAA,WAAQ;;8EACP,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oIAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;kGACpB,8OAAC,oIAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAI1B,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAGxB,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,8OAAC,iIAAA,CAAA,YAAS;oEAAC,WAAU;8EACnB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;2DAxBT;;;;;;;;;;;;;;;;;;;;+CA+BrB,SAAS,MAAM,KAAK,IACtB,cAAc;kDACd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAGrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEAGtD,8OAAC;4DAAE,WAAU;sEAAyD;;;;;;;;;;;;8DAMxE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;;8EAEV,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAInC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAE;sFAAG,8OAAC;sFAAO;;;;;;wEAAmB;;;;;;;8EACjC,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAMX,2BAA2B;kDAC3B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8DACJ,8OAAC,iIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0EACP,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAa;;;;;;;;;;;;;;;;;8DAGtC,8OAAC,iIAAA,CAAA,YAAS;8DACP,SAAS,GAAG,CAAC,CAAC,wBACf,8OAAC,iIAAA,CAAA,WAAQ;;8EACP,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;4EACZ,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,kBACzC,8OAAC;gFACC,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;gFAC1B,KAAK,QAAQ,KAAK;gFAClB,WAAU;;;;;qGAGZ,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;;;;;;0FAGvB,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGAAe,QAAQ,KAAK;;;;;;oFAC1C,QAAQ,QAAQ,kBACf,8OAAC,iIAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAU,WAAU;;0GACjC,8OAAC,kMAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;;;;;;;;;;;;8EAO3C,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;4EACZ,QAAQ,SAAS,kBAChB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAc;;;;;;oFAAW;oFAAE,QAAQ,SAAS;;;;;;;4EAG/D,QAAQ,gBAAgB,kBACvB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAc;;;;;;oFAAe;oFAAE,QAAQ,gBAAgB;;;;;;;4EAG1E,QAAQ,MAAM,kBACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAc;;;;;;oFAAc;oFAAE,QAAQ,MAAM;;;;;;;4EAG/D,QAAQ,OAAO,kBACd,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAc;;;;;;oFAAU;oFAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;;;8EAKjE,8OAAC,iIAAA,CAAA,YAAS;8EACP,eAAe,QAAQ,MAAM;;;;;;8EAEhC,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;kFACZ,WAAW,QAAQ,SAAS;;;;;;;;;;;8EAGjC,8OAAC,iIAAA,CAAA,YAAS;oEAAC,WAAU;8EACnB,cAAA,8OAAC,4IAAA,CAAA,eAAY;;0FACX,8OAAC,4IAAA,CAAA,sBAAmB;gFAAC,OAAO;0FAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,WAAU;8FAChC,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gFAAC,OAAM;;kGACzB,8OAAC,4IAAA,CAAA,oBAAiB;kGAAC;;;;;;kGACnB,8OAAC,4IAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,kBAAkB;;0GACjD,8OAAC,gMAAA,CAAA,MAAG;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGlC,8OAAC,4IAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,kBAAkB;;0GACjD,8OAAC,2MAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGnC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kGACtB,8OAAC,4IAAA,CAAA,mBAAgB;wFACf,SAAS,IAAM,qBAAqB,QAAQ,GAAG;;0GAE/C,8OAAC,kMAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;4FACf,QAAQ,QAAQ,GAAG,yBAAyB;;;;;;;kGAE/C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kGACtB,8OAAC,4IAAA,CAAA,mBAAgB;wFACf,SAAS,IAAM,mBAAmB,QAAQ,GAAG,EAAE,QAAQ,MAAM,KAAK,WAAW,aAAa;kGAEzF,QAAQ,MAAM,KAAK,WAAW,eAAe;;;;;;kGAEhD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kGACtB,8OAAC,4IAAA,CAAA,mBAAgB;wFACf,SAAS,IAAM,oBAAoB;wFACnC,WAAU;;0GAEV,8OAAC,0MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2DA5F9B,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;oCA0GjC,YAAY,SAAS,MAAM,GAAG,mBAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAgC;oDACpC,cAAc,WAAW;oDAAE;oDAAK,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,UAAU,SAAS,MAAM;oDAAE;oDAAK,SAAS,MAAM;oDAAC;;;;;;;0DAExH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wDACxD,UAAU,gBAAgB;kEAC3B;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,cAAc;wDAC5C,UAAU,SAAS,MAAM,GAAG;kEAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAM;wBACN,cAAc;wBACd,WAAU;kCAEV,cAAA,8OAAC,iIAAA,CAAA,eAAY;4BACX,WAAU;4BACV,iBAAiB;4BACjB,SAAS,IAAM,iBAAiB;;8CAEhC,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;;gDACnB,mCAAqB,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;sDAGrE,8OAAC,iIAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;8CAKpB,8OAAC;oCAAI,WAAU;8CACZ,qBAAqB,CAAC,WAAW,KAAK,GACrC,gDAAgD;kDAChD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;0EAEtB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAKxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAItB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI,CAAC;4DAAE,QAAQ;wDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gEAAgB,WAAU;0EACzB,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oIAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oIAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC,oIAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;;;;;;;;;;;;;+DALhB;;;;;;;;;;;;;;;;;;;;;6DAclB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;;kEAEhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,QAAQ;gEACR,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,KAAK;oEACvB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAC1E,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,YAAY;oEAC9B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEACjF,WAAU;;;;;;;;;;;;;;;;;kEAKhB,8OAAC,8IAAA,CAAA,YAAS;wDACR,OAAM;wDACN,aAAY;kEAEZ,cAAA,8OAAC,kJAAA,CAAA,iBAAc;4DACb,SAAS,WAAW,WAAW;4DAC/B,UAAU,CAAC,UAAY,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa;oEAAQ,CAAC;4DAC/E,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;0DAMhB,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;0DAEZ,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DACC,WAAW,CAAC,0EAA0E,EACpF,aACI,gCACA,wCACL,CAAC,EAAE,kBAAkB,mCAAmC,IAAI;4DAC7D,YAAY;4DACZ,aAAa;4DACb,QAAQ;;8EAER,8OAAC;oEACC,MAAK;oEACL,QAAQ;oEACR,QAAO;oEACP,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,iBAAiB,EAAE,MAAM,CAAC,KAAK;oEAClE,WAAU;oEACV,UAAU;;;;;;8EAGZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACZ,gCACC,8OAAC,iNAAA,CAAA,UAAO;gFAAC,WAAU;;;;;qGAEnB,8OAAC,oMAAA,CAAA,QAAS;gFAAC,WAAU;;;;;;;;;;;sFAIzB,8OAAC;;8FACC,8OAAC;oFAAE,WAAU;8FACV,kBAAkB,wBAAwB;;;;;;8FAE7C,8OAAC;oFAAE,WAAU;8FAA6B;;;;;;;;;;;;sFAK5C,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,UAAU;oFACV,WAAU;;sGAEV,8OAAC,sMAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGrC,8OAAC;oFAAK,WAAU;8FAAwB;;;;;;8FACxC,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS;wFACP,MAAM,MAAM,OAAO;wFACnB,IAAI,KAAK,gBAAgB;oFAC3B;oFACA,UAAU;;sGAEV,8OAAC,kMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;wDAQxC,WAAW,MAAM,CAAC,MAAM,GAAG,mBAC1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;;gFAAoC;gFAC9B,WAAW,MAAM,CAAC,MAAM;gFAAC;;;;;;;sFAE7C,8OAAC;4EAAK,WAAU;;gFAAwB;gFAC1B,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,WAAW,MAAM,EAAE,MAAM,GAAG;gFAAM;;;;;;;;;;;;;8EAG5E,8OAAC;oEAAI,WAAU;8EACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC7B,8OAAC;4EAAgB,WAAU;;8FACzB,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFACC,KAAK;wFACL,KAAK,CAAC,cAAc,EAAE,QAAQ,GAAG;wFACjC,WAAU;wFACV,SAAS,CAAC;4FACR,MAAM,SAAS,EAAE,MAAM;4FACvB,OAAO,GAAG,GAAG;wFACf;;;;;;;;;;;8FAGJ,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAU;oFACV,SAAS,IAAM,YAAY;8FAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wFAAC,WAAU;;;;;;;;;;;;2EAnBP;;;;;;;;;;;;;;;;sEA4BlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAyC;;;;;;8EACvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EACJ,aAAY;4EACZ,WAAW,CAAC;gFACV,IAAI,EAAE,GAAG,KAAK,SAAS;oFACrB,MAAM,SAAS,EAAE,MAAM;oFACvB,gBAAgB,OAAO,KAAK;oFAC5B,OAAO,KAAK,GAAG;gFACjB;4EACF;4EACA,WAAU;;;;;;sFAEZ,8OAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,CAAC;gFACR,MAAM,QAAQ,EAAE,aAAa,CAAC,sBAAsB;gFACpD,gBAAgB,MAAM,KAAK;gFAC3B,MAAM,KAAK,GAAG;4EAChB;sFACD;;;;;;;;;;;;8EAIH,8OAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;0DAQhD,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;0DAEZ,cAAA,8OAAC,8IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,aAAY;8DAEZ,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,sBAAsB,YACrB,mCAAmC;0EACnC,8OAAC;gEAAI,WAAU;0EACZ,MAAM,IAAI,CAAC;oEAAE,QAAQ;gEAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wEAAgB,WAAU;kFACzB,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oIAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,oIAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,8OAAC,oIAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,8OAAC,oIAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;;;;;;;;;;;;;uEANhB;;;;;;;;;uEAYZ,qBAAqB,kBAAkB,MAAM,GAAG,kBAClD,8OAAC;gEAAI,WAAU;0EACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,8OAAC;wEAEC,WAAW,CAAC,4EAA4E,EACtF,WAAW,WAAW,CAAC,QAAQ,CAAC,WAAW,YAAY,IACnD,gCACA,mBACJ;wEACF,SAAS;4EACP,cAAc,CAAA,OAAQ,CAAC;oFACrB,GAAG,IAAI;oFACP,aAAa,KAAK,WAAW,CAAC,QAAQ,CAAC,WAAW,YAAY,IAC1D,KAAK,WAAW,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,WAAW,YAAY,IAC5D;2FAAI,KAAK,WAAW;wFAAE,WAAW,YAAY;qFAAC;gFACpD,CAAC;wEACH;kFAEA,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,MAAK;oFACL,SAAS,WAAW,WAAW,CAAC,QAAQ,CAAC,WAAW,YAAY;oFAChE,UAAU,KAAO;oFACjB,WAAU;;;;;;8FAEZ,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAE,WAAU;sGACV,WAAW,KAAK;;;;;;wFAElB,WAAW,WAAW,kBACrB,8OAAC;4FAAE,WAAU;sGACV,WAAW,WAAW;;;;;;sGAG3B,8OAAC;4FAAE,WAAU;;gGACV,WAAW,YAAY,IAAI;gGAAE;;;;;;;;;;;;;;;;;;;uEAhC/B,WAAW,GAAG;;;;;;;;;qFAwCzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,wMAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,8OAAC;wEAAE,WAAU;kFAAU;;;;;;kFACvB,8OAAC;wEAAE,WAAU;kFAAkC;;;;;;kFAG/C,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS;wEACT,WAAU;;0FAEV,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;4DAMtC,WAAW,WAAW,CAAC,MAAM,GAAG,mBAC/B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;4EAAyC;4EAC7B,WAAW,WAAW,CAAC,MAAM;4EAAC;;;;;;;kFAEvD,8OAAC;wEAAI,WAAU;kFACZ,WAAW,WAAW,CAAC,GAAG,CAAC,CAAC;4EAC3B,MAAM,aAAa,mBAAmB,KAAK,CAAA,IAAK,EAAE,YAAY,KAAK;4EACnE,OAAO,2BACL,8OAAC;gFAEC,WAAU;;oFAET,WAAW,KAAK;kGACjB,8OAAC;wFACC,MAAK;wFACL,WAAU;wFACV,SAAS,CAAC;4FACR,EAAE,eAAe;4FACjB,cAAc,CAAA,OAAQ,CAAC;oGACrB,GAAG,IAAI;oGACP,aAAa,KAAK,WAAW,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gGACpD,CAAC;wFACH;kGAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;4FAAC,WAAU;;;;;;;;;;;;+EAfV;;;;uFAkBL;wEACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DASZ,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,SAAS;oEAC3B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAC9E,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,SAAS;oEAC3B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAC9E,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,OAAO,WAAW,MAAM;4EACxB,eAAe,CAAC;gFACd,IAAI,UAAU,UAAU;oFACtB,cAAc,CAAA,OAAQ,CAAC;4FAAE,GAAG,IAAI;4FAAE,QAAQ;wFAAG,CAAC;gFAChD,OAAO;oFACL,cAAc,CAAA,OAAQ,CAAC;4FAAE,GAAG,IAAI;4FAAE,QAAQ;wFAAM,CAAC;gFACnD;4EACF;;8FAEA,8OAAC,kIAAA,CAAA,gBAAa;oFAAC,WAAU;8FACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wFAAC,aAAY;;;;;;;;;;;8FAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sGACZ,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAQ;;;;;;sGAC1B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAQ;;;;;;sGAC1B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAS;;;;;;sGAC3B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAS;;;;;;sGAC3B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAS;;;;;;;;;;;;;;;;;;wEAG9B,CAAC,CAAC,WAAW,MAAM,IAAI,CAAC;4EAAC;4EAAO;4EAAO;4EAAO;4EAAO;4EAAO;4EAAS;4EAAS;4EAAU;yEAAS,CAAC,QAAQ,CAAC,WAAW,MAAM,CAAC,mBAC5H,8OAAC,iIAAA,CAAA,QAAK;4EACJ,aAAY;4EACZ,OAAO,WAAW,MAAM;4EACxB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wFAAE,GAAG,IAAI;wFAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oFAAC,CAAC;4EAC3E,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAOpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,gBAAgB;oEAClC,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEACrF,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,aAAY;4EACZ,OAAO,WAAW,eAAe,CAAC,OAAO,CAAC,UAAU;4EACpD,UAAU,CAAC;gFACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gFAC5B,cAAc,CAAA,OAAQ,CAAC;wFACrB,GAAG,IAAI;wFACP,iBAAiB,QAAQ,GAAG,MAAM,MAAM,CAAC,GAAG;oFAC9C,CAAC;4EACH;4EACA,WAAU;4EACV,MAAK;4EACL,KAAI;;;;;;sFAEN,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAK,WAAU;0FAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAMpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,UAAU;oEAC5B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAC/E,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,WAAW,UAAU;oEAC5B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAC/E,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,OAAO,WAAW,OAAO;4EACzB,eAAe,CAAC;gFACd,IAAI,UAAU,UAAU;oFACtB,cAAc,CAAA,OAAQ,CAAC;4FAAE,GAAG,IAAI;4FAAE,SAAS;wFAAG,CAAC;gFACjD,OAAO;oFACL,cAAc,CAAA,OAAQ,CAAC;4FAAE,GAAG,IAAI;4FAAE,SAAS;wFAAM,CAAC;gFACpD;4EACF;;8FAEA,8OAAC,kIAAA,CAAA,gBAAa;oFAAC,WAAU;8FACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wFAAC,aAAY;;;;;;;;;;;8FAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sGACZ,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAM;;;;;;sGACxB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAO;;;;;;sGACzB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAO;;;;;;sGACzB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAO;;;;;;sGACzB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAO;;;;;;sGACzB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAO;;;;;;sGACzB,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAU;;;;;;sGAC5B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAU;;;;;;sGAC5B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAU;;;;;;sGAC5B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAU;;;;;;sGAC5B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAU;;;;;;sGAC5B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAS;;;;;;;;;;;;;;;;;;wEAG9B,CAAC,CAAC,WAAW,OAAO,IAAI,CAAC;4EAAC;4EAAO;4EAAO;4EAAO;4EAAO;4EAAO;4EAAO;4EAAO;4EAAO;4EAAO;4EAAQ;4EAAQ;4EAAQ;4EAAQ;4EAAQ;4EAAW;4EAAW;4EAAW;4EAAW;yEAAU,CAAC,QAAQ,CAAC,WAAW,OAAO,CAAC,mBACnN,8OAAC,iIAAA,CAAA,QAAK;4EACJ,aAAY;4EACZ,OAAO,WAAW,OAAO;4EACzB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wFAAE,GAAG,IAAI;wFAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oFAAC,CAAC;4EAC5E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAStB,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;;kEAEZ,8OAAC,8IAAA,CAAA,YAAS;wDACR,OAAM;wDACN,aAAY;kEAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,OAAO,WAAW,SAAS;4DAC3B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC9E,WAAU;;;;;;;;;;;kEAId,8OAAC,8IAAA,CAAA,YAAS;wDACR,OAAM;wDACN,aAAY;kEAEZ,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,aAAY;4DACZ,OAAO,WAAW,QAAQ;4DAC1B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC7E,MAAM;4DACN,WAAU;;;;;;;;;;;kEAId,8OAAC,8IAAA,CAAA,YAAS;wDACR,OAAM;wDACN,aAAY;kEAEZ,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,aAAY;4DACZ,OAAO,WAAW,YAAY;4DAC9B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACjF,MAAM;4DACN,WAAU;;;;;;;;;;;;;;;;;0DAQhB,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;0DAEZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,WAAW,MAAM;gEACxB,eAAe,CAAC,QAAU,cAAc,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ;wEAAM,CAAC;;kFAE3E,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAW;;;;;;0FAC7B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAiB;;;;;;;;;;;;;;;;;;;;;;;sEAKzC,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,SAAS,WAAW,QAAQ;wEAC5B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,UAAU,EAAE,MAAM,CAAC,OAAO;gFAAC,CAAC;wEAC/E,WAAU;;;;;;kFAEZ,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWpE,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB;4CAChC,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM;4CACtC,WAAU;sDAET,kCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAU7C,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAM;wBACN,cAAc;wBACd,WAAU;kCAEV,cAAA,8OAAC,iIAAA,CAAA,eAAY;4BACX,WAAU;4BACV,iBAAiB;4BACjB,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG7B,8OAAC,iIAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;8CAKpB,8OAAC;oCAAI,WAAU;8CACZ,iCACC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAkH;;;;;;;;;;;kEAIlI,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAM,WAAU;0FAA4C;;;;;;0FAC7D,8OAAC;gFAAI,WAAU;0FAAyB,gBAAgB,KAAK;;;;;;;;;;;;oEAE9D,gBAAgB,YAAY,kBAC3B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAM,WAAU;0FAA4C;;;;;;0FAC7D,8OAAC;gFAAI,WAAU;0FAAa,gBAAgB,YAAY;;;;;;;;;;;;;;;;;;4DAI7D,gBAAgB,WAAW,kBAC1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,WAAU;kFAA4C;;;;;;kFAC7D,8OAAC;wEACC,WAAU;wEACV,yBAAyB;4EAAE,QAAQ,gBAAgB,WAAW;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;0DAQzE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAkH;;;;;;;;;;;kEAIlI,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,gBAAgB,SAAS,kBACxB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAuB,gBAAgB,SAAS;;;;;;;;;;;;gEAGlE,gBAAgB,gBAAgB,kBAC/B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAuB,gBAAgB,gBAAgB;;;;;;;;;;;;gEAGzE,gBAAgB,eAAe,kBAC9B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAa,gBAAgB,eAAe;;;;;;;;;;;;gEAG9D,gBAAgB,MAAM,kBACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAa,gBAAgB,MAAM;;;;;;;;;;;;gEAGrD,gBAAgB,UAAU,kBACzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAa,gBAAgB,UAAU;;;;;;;;;;;;gEAGzD,gBAAgB,UAAU,kBACzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAa,gBAAgB,UAAU;;;;;;;;;;;;gEAGzD,gBAAgB,OAAO,kBACtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;4EAAI,WAAU;sFAAa,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAQ5D,gBAAgB,MAAM,IAAI,gBAAgB,MAAM,CAAC,MAAM,GAAG,mBACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAkH;;;;;;;;;;;kEAIlI,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACZ,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAY,sBACvC,8OAAC;oEAAgB,WAAU;8EACzB,cAAA,8OAAC;wEACC,KAAK,MAAM,GAAG;wEACd,KAAK,CAAC,cAAc,EAAE,QAAQ,GAAG;wEACjC,WAAU;wEACV,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE;;;;;;mEALhC;;;;;;;;;;;;;;;;;;;;;4CAenB,gBAAgB,WAAW,IAAI,gBAAgB,WAAW,CAAC,MAAM,GAAG,mBACnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAkH;;;;;;;;;;;kEAIlI,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACZ,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAC;gEAChC,MAAM,aAAa,mBAAmB,KAAK,CAAA,IAAK,EAAE,YAAY,KAAK;gEACnE,OAAO,2BACL,8OAAC,iIAAA,CAAA,QAAK;oEAEJ,SAAQ;oEACR,WAAU;8EAET,WAAW,KAAK;mEAJZ;;;;yFAOP,8OAAC,iIAAA,CAAA,QAAK;oEAEJ,SAAQ;oEACR,WAAU;8EACX;mEAHM;;;;;4DAOX;;;;;;;;;;;;;;;;;0DAOR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAkH;;;;;;;;;;;kEAIlI,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;sFAAK,eAAe,gBAAgB,MAAM;;;;;;;;;;;;8EAE7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAA4C;;;;;;sFAC7D,8OAAC;sFACE,gBAAgB,QAAQ,iBACvB,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;;kGACjC,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;qGAInC,8OAAC;gFAAK,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW1D,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,kBAAkB;sDAClC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;gDACP,kBAAkB;gDAClB,kBAAkB;4CACpB;;8DAEA,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAM;wBACN,cAAc,CAAC;4BACb,kBAAkB;4BAClB,IAAI,CAAC,MAAM;gCACT,mBAAmB;4BACrB;wBACF;wBACA,WAAU;kCAEV,cAAA,8OAAC,iIAAA,CAAA,eAAY;4BACX,WAAU;4BACV,iBAAiB;4BACjB,SAAS;gCACP,kBAAkB;gCAClB,mBAAmB;4BACrB;;8CAEA,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;;gDACnB,mCAAqB,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;sDAGrE,8OAAC,iIAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;8CAKpB,8OAAC;oCAAI,WAAU;8CACZ,CAAC,kBACA,kCAAkC;kDAClC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;0EAEtB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAKxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI,CAAC;4DAAE,QAAQ;wDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;+DAFZ;;;;;;;;;;;;;;;;0DAShB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI,CAAC;4DAAE,QAAQ;wDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC,oIAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;;;+DAJd;;;;;;;;;;;;;;;;;;;;;6DAYlB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;;kEAEd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,QAAQ;gEACR,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,YAAY,KAAK;oEACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAC3E,WAAU;;;;;;;;;;;0EAId,8OAAC,8IAAA,CAAA,YAAS;gEACR,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,YAAY,YAAY;oEAC/B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAClF,WAAU;;;;;;;;;;;;;;;;;kEAKhB,8OAAC,8IAAA,CAAA,YAAS;wDACR,OAAM;wDACN,aAAY;kEAEZ,cAAA,8OAAC,kJAAA,CAAA,iBAAc;4DACb,SAAS,YAAY,WAAW;4DAChC,UAAU,CAAC,UAAY,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa;oEAAQ,CAAC;4DAChF,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;0DAMhB,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;0DAEZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,YAAY,SAAS;gEAC5B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC/E,WAAU;;;;;;;;;;;sEAId,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,YAAY,gBAAgB;gEACnC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACtF,WAAU;;;;;;;;;;;sEAId,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,aAAY;wEACZ,OAAO,YAAY,eAAe,CAAC,OAAO,CAAC,UAAU;wEACrD,UAAU,CAAC;4EACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4EAC5B,eAAe,CAAA,OAAQ,CAAC;oFACtB,GAAG,IAAI;oFACP,iBAAiB,QAAQ,GAAG,MAAM,MAAM,CAAC,GAAG;gFAC9C,CAAC;wEACH;wEACA,WAAU;wEACV,MAAK;wEACL,KAAI;;;;;;kFAEN,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAA4C;;;;;;;;;;;;;;;;;;;;;;sEAKlE,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,YAAY,MAAM;wEACzB,eAAe,CAAC;4EACd,IAAI,UAAU,UAAU;gFACtB,eAAe,CAAA,OAAQ,CAAC;wFAAE,GAAG,IAAI;wFAAE,QAAQ;oFAAG,CAAC;4EACjD,OAAO;gFACL,eAAe,CAAA,OAAQ,CAAC;wFAAE,GAAG,IAAI;wFAAE,QAAQ;oFAAM,CAAC;4EACpD;wEACF;;0FAEA,8OAAC,kIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kGACZ,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAQ;;;;;;kGAC1B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAQ;;;;;;kGAC1B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;;;;;;;;;;;;;oEAG9B,CAAC,CAAC,YAAY,MAAM,IAAI,CAAC;wEAAC;wEAAO;wEAAO;wEAAO;wEAAO;wEAAO;wEAAS;wEAAS;wEAAU;qEAAS,CAAC,QAAQ,CAAC,YAAY,MAAM,CAAC,mBAC9H,8OAAC,iIAAA,CAAA,QAAK;wEACJ,aAAY;wEACZ,OAAO,YAAY,MAAM;wEACzB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC5E,WAAU;;;;;;;;;;;;;;;;;sEAMlB,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,YAAY,UAAU;gEAC7B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAChF,WAAU;;;;;;;;;;;sEAId,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,YAAY,UAAU;gEAC7B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAChF,WAAU;;;;;;;;;;;sEAId,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,YAAY,OAAO;wEAC1B,eAAe,CAAC;4EACd,IAAI,UAAU,UAAU;gFACtB,eAAe,CAAA,OAAQ,CAAC;wFAAE,GAAG,IAAI;wFAAE,SAAS;oFAAG,CAAC;4EAClD,OAAO;gFACL,eAAe,CAAA,OAAQ,CAAC;wFAAE,GAAG,IAAI;wFAAE,SAAS;oFAAM,CAAC;4EACrD;wEACF;;0FAEA,8OAAC,kIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kGACZ,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAM;;;;;;kGACxB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;;;;;;;;;;;;;oEAG9B,CAAC,CAAC,YAAY,OAAO,IAAI,CAAC;wEAAC;wEAAO;wEAAO;wEAAO;wEAAO;wEAAO;wEAAO;wEAAO;wEAAO;wEAAO;wEAAQ;wEAAQ;wEAAQ;wEAAQ;wEAAQ;wEAAW;wEAAW;wEAAW;wEAAW;qEAAU,CAAC,QAAQ,CAAC,YAAY,OAAO,CAAC,mBACrN,8OAAC,iIAAA,CAAA,QAAK;wEACJ,aAAY;wEACZ,OAAO,YAAY,OAAO;wEAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAStB,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;0DAEZ,cAAA,8OAAC,8IAAA,CAAA,YAAS;oDACR,OAAM;oDACN,aAAY;8DAEZ,cAAA,8OAAC;wDAAI,WAAU;kEACZ,sBAAsB,YACrB,mCAAmC;sEACnC,8OAAC;4DAAI,WAAU;sEACZ,MAAM,IAAI,CAAC;gEAAE,QAAQ;4DAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC,oIAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oIAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC,oIAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;;;;;;;;mEAJd;;;;;;;;;mEASZ,qBAAqB,kBAAkB,MAAM,GAAG,kBAClD,8OAAC;4DAAI,WAAU;sEACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC;4EACC,MAAK;4EACL,IAAI,CAAC,gBAAgB,EAAE,WAAW,GAAG,EAAE;4EACvC,SAAS,YAAY,WAAW,CAAC,QAAQ,CAAC,WAAW,YAAY;4EACjE,UAAU,CAAC;gFACT,MAAM,eAAe,WAAW,YAAY;gFAC5C,eAAe,CAAA,OAAQ,CAAC;wFACtB,GAAG,IAAI;wFACP,aAAa,EAAE,MAAM,CAAC,OAAO,GACzB;+FAAI,KAAK,WAAW;4FAAE;yFAAa,GACnC,KAAK,WAAW,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;oFAC3C,CAAC;4EACH;4EACA,WAAU;;;;;;sFAEZ,8OAAC;4EACC,SAAS,CAAC,gBAAgB,EAAE,WAAW,GAAG,EAAE;4EAC5C,WAAU;;8FAEV,8OAAC;oFAAI,WAAU;8FAAuB,WAAW,KAAK;;;;;;gFACrD,WAAW,WAAW,kBACrB,8OAAC;oFAAI,WAAU;8FACZ,WAAW,WAAW;;;;;;;;;;;;;mEAzBxB,WAAW,GAAG;;;;;;;;;iFAiCzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,8OAAC;oEAAE,WAAU;8EAAU;;;;;;8EACvB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAUX,8OAAC,8IAAA,CAAA,cAAW;gDACV,OAAM;gDACN,aAAY;0DAEZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,YAAY,MAAM;gEACzB,eAAe,CAAC,QAAU,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ;wEAAM,CAAC;;kFAE5E,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAW;;;;;;0FAC7B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAe;;;;;;0FACjC,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAiB;;;;;;;;;;;;;;;;;;;;;;;sEAKzC,8OAAC,8IAAA,CAAA,YAAS;4DACR,OAAM;4DACN,aAAY;sEAEZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,SAAS,YAAY,QAAQ;wEAC7B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,UAAU,EAAE,MAAM,CAAC,OAAO;gFAAC,CAAC;wEAChF,WAAU;;;;;;kFAEZ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAsB;;;;;;0FACrC,8OAAC;gFAAI,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAY7D,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,kBAAkB;gDAClB,mBAAmB;4CACrB;4CACA,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY,KAAK,CAAC,IAAI,MAAM;4CACvC,WAAU;sDAET,kCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAU7C,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAM;wBACN,cAAc;wBACd,WAAU;kCAEV,cAAA,8OAAC,iIAAA,CAAA,eAAY;4BACX,WAAU;4BACV,iBAAiB;4BACjB,SAAS;gCACP,oBAAoB;gCACpB,mBAAmB;4BACrB;;8CAEA,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGhC,8OAAC,iIAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;8CAKpB,8OAAC;oCAAI,WAAU;8CACZ,iCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,gBAAgB,MAAM,IAAI,gBAAgB,MAAM,CAAC,MAAM,GAAG,kBACzD,8OAAC;gEACC,KAAK,gBAAgB,MAAM,CAAC,EAAE,CAAC,GAAG;gEAClC,KAAK,gBAAgB,KAAK;gEAC1B,WAAU;;;;;qFAGZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAIzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,gBAAgB,KAAK;;;;;;gEAEvB,gBAAgB,SAAS,kBACxB,8OAAC;oEAAE,WAAU;;wEAAwB;wEAC7B,gBAAgB,SAAS;;;;;;;gEAGlC,gBAAgB,WAAW,IAAI,gBAAgB,WAAW,CAAC,MAAM,GAAG,mBACnE,8OAAC;oEAAE,WAAU;;wEAAwB;wEACrB,gBAAgB,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0DAM1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAmB;;;;;;kEAChC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOd,8OAAC,iIAAA,CAAA,cAAW;;sDACV,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,oBAAoB;gDACpB,mBAAmB;4CACrB;4CACA,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,kCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD", "debugId": null}}]}