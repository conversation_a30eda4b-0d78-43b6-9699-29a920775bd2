"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";

export default function SetupAdminPage() {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  
  const updateAdminRoleAndPermissions = useMutation(api.admins.updateAdminRoleAndPermissions);

  const handleSetupAdmin = async () => {
    setStatus('loading');
    setMessage('');
    
    try {
      await updateAdminRoleAndPermissions({
        email: "<EMAIL>",
        role: "super_admin",
        permissions: [
          "users.read", "users.write", "users.approve", "users.delete",
          "products.read", "products.write", "products.delete",
          "admins.read", "admins.write", "admins.delete",
          "settings.read", "settings.write",
          "api_keys.read", "api_keys.write", "api_keys.delete",
          "notifications.read", "notifications.write",
          "logs.read", "reports.read"
        ]
      });
      
      setStatus('success');
      setMessage('Admin role and permissions updated successfully! Please refresh the page to see the navigation menu.');
    } catch (error) {
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Failed to update admin');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-xl">B</span>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Setup Admin Permissions
          </CardTitle>
          <CardDescription className="text-center">
            Update your admin account to have full permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {status === 'success' && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
          
          {status === 'error' && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
          
          <Button
            onClick={handleSetupAdmin}
            className="w-full"
            disabled={status === 'loading' || status === 'success'}
          >
            {status === 'loading' ? 'Setting up...' : 
             status === 'success' ? 'Setup Complete!' : 
             'Setup Admin Permissions'}
          </Button>
          
          {status === 'success' && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={() => window.location.href = '/dashboard'}
                className="w-full"
              >
                Go to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
