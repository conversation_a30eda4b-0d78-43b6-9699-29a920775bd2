{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "93q2+ThywVMICBZGrjqWrlW2FJpdwcjpUekbGqXbIAA=", "__NEXT_PREVIEW_MODE_ID": "68b97ff660392dafa4f2b8c6fc8cb753", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "67b35502eaf0cd8d30db6b210774b84a726e08ead06fb92ffe94c24e5d5efb54", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e7bba35868962a8fbba144bb27ee072d7c9cddd8abe3f040e6e229a537010abd"}}}, "sortedMiddleware": ["/"], "functions": {}}