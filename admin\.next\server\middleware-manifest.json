{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "93q2+ThywVMICBZGrjqWrlW2FJpdwcjpUekbGqXbIAA=", "__NEXT_PREVIEW_MODE_ID": "1a57a8332c3aeb120d6cc2fe0c980d0c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ca277fd87116747656b79b0f4f63ddb26fa69edee386a3d4176367d0eb8716fd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f7b6b9157a4536fb326cde19cc57f11153b48fea704ddb028877359c1ae7f95"}}}, "sortedMiddleware": ["/"], "functions": {}}