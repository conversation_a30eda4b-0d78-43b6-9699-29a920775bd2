"use client";

import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Users,
  Package,
  Bell,
  Settings,
  Key,
  Activity,
  UserCog,
  LogOut,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

interface SidebarProps {
  onNavigate?: () => void;
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  children?: NavItem[];
  permission?: string;
}

export function Sidebar({ onNavigate }: SidebarProps) {
  const { admin, logout, hasPermission } = useAuth();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  // Early return if auth context is not ready
  if (!hasPermission) {
    return (
      <div className="flex h-full flex-col bg-card border-r">
        <div className="flex h-16 items-center border-b px-6">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">B</span>
            </div>
            <div>
              <h2 className="text-lg font-semibold">Benzochem</h2>
              <p className="text-xs text-muted-foreground">Admin Dashboard</p>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">Loading...</div>
        </div>
      </div>
    );
  }

  const navItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "User Management",
      icon: Users,
      permission: "users.read",
      children: [
        {
          title: "All Users",
          href: "/dashboard/users",
          icon: Users,
          permission: "users.read",
        },
        {
          title: "Pending Approvals",
          href: "/dashboard/users/pending",
          icon: Users,
          badge: "3", // This would come from real data
          permission: "users.approve",
        },
        {
          title: "GST Verification",
          href: "/dashboard/users/gst",
          icon: Users,
          permission: "users.read",
        },
      ],
    },
    {
      title: "Product Management",
      icon: Package,
      permission: "products.read",
      children: [
        {
          title: "All Products",
          href: "/dashboard/products",
          icon: Package,
          permission: "products.read",
        },
        {
          title: "Featured Products",
          href: "/dashboard/products/featured",
          icon: Package,
          permission: "products.read",
        },
        {
          title: "Collections",
          href: "/dashboard/products/collections",
          icon: Package,
          permission: "products.read",
        },
      ],
    },
    {
      title: "Notifications",
      href: "/dashboard/notifications",
      icon: Bell,
      badge: "5", // This would come from real data
      permission: "notifications.read",
    },
    {
      title: "Activity Logs",
      href: "/dashboard/activity",
      icon: Activity,
      permission: "logs.read",
    },
    {
      title: "System",
      icon: Settings,
      permission: "settings.read",
      children: [
        {
          title: "Settings",
          href: "/dashboard/settings",
          icon: Settings,
          permission: "settings.read",
        },
        {
          title: "API Keys",
          href: "/dashboard/api-keys",
          icon: Key,
          permission: "api_keys.read",
        },
        {
          title: "Admin Users",
          href: "/dashboard/admins",
          icon: UserCog,
          permission: "admins.read",
        },
      ],
    },
  ];

  const filteredNavItems = navItems.filter(item =>
    !item.permission || (hasPermission && hasPermission(item.permission))
  );

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const isActive = item.href ? pathname === item.href : false;
    const hasItemPermission = !item.permission || (hasPermission && hasPermission(item.permission));

    if (!hasItemPermission) return null;

    const filteredChildren = item.children?.filter(child =>
      !child.permission || (hasPermission && hasPermission(child.permission))
    );

    if (hasChildren && (!filteredChildren || filteredChildren.length === 0)) {
      return null;
    }

    const content = (
      <div className={cn("space-y-1", level > 0 && "ml-4")}>
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start gap-2 h-9",
            level > 0 && "h-8 text-sm"
          )}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.title);
            } else if (item.href) {
              onNavigate?.();
            }
          }}
          asChild={!hasChildren}
        >
          {hasChildren ? (
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <item.icon className="h-4 w-4" />
                <span>{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </div>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </div>
          ) : (
            <Link href={item.href!} className="flex items-center gap-2 w-full">
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
            </Link>
          )}
        </Button>

        {hasChildren && isExpanded && filteredChildren && (
          <div className="space-y-1">
            {filteredChildren.map((child) => (
              <div key={child.title}>
                {renderNavItem(child, level + 1)}
              </div>
            ))}
          </div>
        )}
      </div>
    );

    return content;
  };

  return (
    <div className="flex h-full flex-col bg-card border-r">
      {/* Logo */}
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-lg">B</span>
          </div>
          <div>
            <h2 className="text-lg font-semibold">Benzochem</h2>
            <p className="text-xs text-muted-foreground">Admin Dashboard</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-auto p-4">
        <nav className="space-y-2">
          {filteredNavItems.map((item) => (
            <div key={item.title}>
              {renderNavItem(item)}
            </div>
          ))}
        </nav>
      </div>

      {/* User info and logout */}
      <div className="border-t p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
            <span className="text-sm font-medium">
              {admin?.firstName?.[0]}{admin?.lastName?.[0]}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {admin?.firstName} {admin?.lastName}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {admin?.email}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start gap-2"
          onClick={logout}
        >
          <LogOut className="h-4 w-4" />
          Sign out
        </Button>
      </div>
    </div>
  );
}
