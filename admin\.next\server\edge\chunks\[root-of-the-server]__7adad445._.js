(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__7adad445._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/session.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearSessionCookies": (()=>clearSessionCookies),
    "createSessionToken": (()=>createSessionToken),
    "generateCSRFToken": (()=>generateCSRFToken),
    "getServerSession": (()=>getServerSession),
    "getSessionFromRequest": (()=>getSessionFromRequest),
    "refreshSessionToken": (()=>refreshSessionToken),
    "sessionOptions": (()=>sessionOptions),
    "setCSRFCookie": (()=>setCSRFCookie),
    "setSessionCookie": (()=>setSessionCookie),
    "validateCSRFToken": (()=>validateCSRFToken),
    "verifySessionToken": (()=>verifySessionToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/sign.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/verify.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/headers.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/cookies.js [middleware-edge] (ecmascript)");
;
;
// Session configuration
const SESSION_COOKIE_NAME = 'benzochem-admin-session';
const CSRF_COOKIE_NAME = 'benzochem-csrf-token';
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const CSRF_TOKEN_LENGTH = 32;
// Get JWT secret from environment variable
function getJWTSecret() {
    const secret = process.env.JWT_SECRET || 'benzochem-admin-jwt-secret-change-in-production';
    return new TextEncoder().encode(secret);
}
// Get session encryption key from environment variable
function getSessionKey() {
    return process.env.SESSION_SECRET || 'benzochem-session-secret-must-be-at-least-32-characters-long-change-in-production';
}
function generateCSRFToken() {
    const array = new Uint8Array(CSRF_TOKEN_LENGTH);
    crypto.getRandomValues(array);
    return Array.from(array, (byte)=>byte.toString(16).padStart(2, '0')).join('');
}
async function createSessionToken(adminData) {
    const now = Date.now();
    const expiresAt = now + SESSION_DURATION;
    const payload = {
        ...adminData,
        loginTime: now,
        expiresAt
    };
    const token = await new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SignJWT"](payload).setProtectedHeader({
        alg: 'HS256'
    }).setIssuedAt(now / 1000).setExpirationTime(expiresAt / 1000).setIssuer('benzochem-admin').setAudience('benzochem-admin-dashboard').sign(getJWTSecret());
    return token;
}
async function verifySessionToken(token) {
    try {
        console.log('verifySessionToken: Verifying token...');
        const { payload } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["jwtVerify"])(token, getJWTSecret(), {
            issuer: 'benzochem-admin',
            audience: 'benzochem-admin-dashboard'
        });
        const session = payload;
        console.log('verifySessionToken: Token verified, checking expiration...');
        console.log('verifySessionToken: Session expires at:', new Date(session.expiresAt));
        console.log('verifySessionToken: Current time:', new Date());
        // Check if session has expired
        if (session.expiresAt < Date.now()) {
            console.log('verifySessionToken: Session has expired');
            return null;
        }
        console.log('verifySessionToken: Session is valid');
        return session;
    } catch (error) {
        console.error('Session token verification failed:', error);
        return null;
    }
}
function setSessionCookie(response, token) {
    const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
    response.cookies.set(SESSION_COOKIE_NAME, token, {
        httpOnly: true,
        secure: isProduction,
        sameSite: 'lax',
        maxAge: SESSION_DURATION / 1000,
        path: '/'
    });
}
function setCSRFCookie(response, csrfToken) {
    const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
    response.cookies.set(CSRF_COOKIE_NAME, csrfToken, {
        httpOnly: false,
        secure: isProduction,
        sameSite: 'lax',
        maxAge: SESSION_DURATION / 1000,
        path: '/'
    });
}
async function getSessionFromRequest(request) {
    const token = request.cookies.get(SESSION_COOKIE_NAME)?.value;
    console.log('getSessionFromRequest: Token found:', !!token);
    if (!token) {
        console.log('getSessionFromRequest: No token found');
        return null;
    }
    const session = await verifySessionToken(token);
    console.log('getSessionFromRequest: Session verified:', !!session);
    return session;
}
async function getServerSession() {
    const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
    const token = cookieStore.get(SESSION_COOKIE_NAME)?.value;
    if (!token) {
        return null;
    }
    return await verifySessionToken(token);
}
function clearSessionCookies(response) {
    response.cookies.delete(SESSION_COOKIE_NAME);
    response.cookies.delete(CSRF_COOKIE_NAME);
}
function validateCSRFToken(request, providedToken) {
    const cookieToken = request.cookies.get(CSRF_COOKIE_NAME)?.value;
    return cookieToken === providedToken && cookieToken !== undefined;
}
const sessionOptions = {
    cookieName: SESSION_COOKIE_NAME,
    password: getSessionKey(),
    cookieOptions: {
        secure: ("TURBOPACK compile-time value", "development") === 'production',
        httpOnly: true,
        sameSite: 'lax',
        maxAge: SESSION_DURATION / 1000
    }
};
async function refreshSessionToken(currentToken) {
    const session = await verifySessionToken(currentToken);
    if (!session) {
        return null;
    }
    // Create new token with extended expiration
    const refreshedSession = {
        adminId: session.adminId,
        email: session.email,
        firstName: session.firstName,
        lastName: session.lastName,
        role: session.role,
        permissions: session.permissions,
        isActive: session.isActive
    };
    return await createSessionToken(refreshedSession);
}
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$session$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/session.ts [middleware-edge] (ecmascript)");
;
;
// Define protected routes that require authentication
const PROTECTED_ROUTES = [
    '/dashboard',
    '/users',
    '/products',
    '/orders',
    '/inventory',
    '/reports',
    '/collections',
    '/settings',
    '/api/admin'
];
// Define public routes that don't require authentication
const PUBLIC_ROUTES = [
    '/login',
    '/api/auth/login',
    '/api/auth/logout'
];
// Define routes that should redirect to dashboard if already authenticated
const AUTH_ROUTES = [
    '/login'
];
async function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip middleware for static files and Next.js internals
    if (pathname.startsWith('/_next/') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/static/') || pathname.includes('.')) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Check if the route is protected
    const isProtectedRoute = PROTECTED_ROUTES.some((route)=>pathname.startsWith(route));
    // Check if the route is public
    const isPublicRoute = PUBLIC_ROUTES.some((route)=>pathname === route || pathname.startsWith(route));
    // Check if the route is an auth route (login/setup)
    const isAuthRoute = AUTH_ROUTES.some((route)=>pathname === route || pathname.startsWith(route));
    try {
        // Get session from request
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$session$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getSessionFromRequest"])(request);
        const isAuthenticated = !!session && session.isActive;
        // Handle protected routes
        if (isProtectedRoute) {
            if (!isAuthenticated) {
                // Redirect to login if not authenticated
                const loginUrl = new URL('/login', request.url);
                loginUrl.searchParams.set('redirect', pathname);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
            }
            // Add session info to request headers for API routes
            if (pathname.startsWith('/api/admin')) {
                const requestHeaders = new Headers(request.headers);
                requestHeaders.set('x-admin-id', session.adminId);
                requestHeaders.set('x-admin-role', session.role);
                requestHeaders.set('x-admin-permissions', JSON.stringify(session.permissions));
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
                    request: {
                        headers: requestHeaders
                    }
                });
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        }
        // Handle auth routes (login only)
        if (isAuthRoute) {
            if (isAuthenticated) {
                // Redirect to dashboard if already authenticated
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            }
            // For login route, always allow access
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        }
        // Handle public routes
        if (isPublicRoute) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        }
        // Handle root route
        if (pathname === '/') {
            if (isAuthenticated) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            } else {
                // Always redirect unauthenticated users to login
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', request.url));
            }
        }
        // Default: allow the request to continue
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    } catch (error) {
        console.error('Middleware error:', error);
        // On error, redirect to login for protected routes
        if (isProtectedRoute) {
            const loginUrl = new URL('/login', request.url);
            loginUrl.searchParams.set('redirect', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
        }
        // For other routes, continue normally
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__7adad445._.js.map