import { NextRequest, NextResponse } from 'next/server';
import { api } from '../../../../convex/_generated/api';
import { ConvexHttpClient } from 'convex/browser';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Update the admin user to be a super_admin with all permissions
    await convex.mutation(api.admins.updateAdminRoleAndPermissions, {
      email: email,
      role: "super_admin",
      permissions: [
        "users.read", "users.write", "users.approve", "users.delete",
        "products.read", "products.write", "products.delete",
        "admins.read", "admins.write", "admins.delete",
        "settings.read", "settings.write",
        "api_keys.read", "api_keys.write", "api_keys.delete",
        "notifications.read", "notifications.write",
        "logs.read", "reports.read"
      ]
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Admin role and permissions updated successfully' 
    });
  } catch (error) {
    console.error('Setup admin error:', error);
    return NextResponse.json({ 
      error: 'Failed to setup admin', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
